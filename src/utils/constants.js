// StoreTok App Constants

// Screen Names
export const SCREENS = {
  HOME: 'Home',
  DISCOVER: 'Discover',
  INBOX: 'Inbox',
  CART: 'Cart',
  PROFILE: 'Profile',
  AUTH: 'Auth',
  PRODUCT_DETAIL: 'ProductDetail',
  TRADER: 'Trader',
  COMMENTS: 'Comments',
  LIVE_SHOPPING: 'LiveShopping',
  SETTINGS: 'Settings',
  ORDER_HISTORY: 'OrderHistory',
  WISHLIST: 'Wishlist',
};

// User Roles
export const USER_ROLES = {
  CUSTOMER: 'customer',
  SELLER: 'seller',
  ADMIN: 'admin',
  MODERATOR: 'moderator',
};

// Product Categories
export const PRODUCT_CATEGORIES = {
  ELECTRONICS: 'electronics',
  FASHION: 'fashion',
  HOME_GARDEN: 'home_garden',
  SPORTS: 'sports',
  BEAUTY: 'beauty',
  BOOKS: 'books',
  TOYS: 'toys',
  FOOD: 'food',
  AUTOMOTIVE: 'automotive',
  HEALTH: 'health',
};

// Order Status
export const ORDER_STATUS = {
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  PROCESSING: 'processing',
  SHIPPED: 'shipped',
  DELIVERED: 'delivered',
  CANCELLED: 'cancelled',
  REFUNDED: 'refunded',
};

// Payment Status
export const PAYMENT_STATUS = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  FAILED: 'failed',
  REFUNDED: 'refunded',
  CANCELLED: 'cancelled',
};

// Notification Types
export const NOTIFICATION_TYPES = {
  ORDER: 'order',
  SOCIAL: 'social',
  MARKETING: 'marketing',
  SYSTEM: 'system',
  LIVE_EVENT: 'live_event',
};

// Media Types
export const MEDIA_TYPES = {
  IMAGE: 'image',
  VIDEO: 'video',
  AUDIO: 'audio',
  DOCUMENT: 'document',
};

// Supported Image Formats
export const IMAGE_FORMATS = [
  'jpg',
  'jpeg',
  'png',
  'gif',
  'webp',
  'bmp',
];

// Supported Video Formats
export const VIDEO_FORMATS = [
  'mp4',
  'mov',
  'avi',
  'mkv',
  'webm',
  'm4v',
];

// Social Actions
export const SOCIAL_ACTIONS = {
  LIKE: 'like',
  UNLIKE: 'unlike',
  COMMENT: 'comment',
  SHARE: 'share',
  FOLLOW: 'follow',
  UNFOLLOW: 'unfollow',
  BLOCK: 'block',
  REPORT: 'report',
};

// Cart Actions
export const CART_ACTIONS = {
  ADD: 'add',
  REMOVE: 'remove',
  UPDATE_QUANTITY: 'update_quantity',
  CLEAR: 'clear',
  APPLY_COUPON: 'apply_coupon',
  REMOVE_COUPON: 'remove_coupon',
};

// Search Filters
export const SEARCH_FILTERS = {
  PRICE_RANGE: 'price_range',
  CATEGORY: 'category',
  BRAND: 'brand',
  RATING: 'rating',
  AVAILABILITY: 'availability',
  SHIPPING: 'shipping',
  DISCOUNT: 'discount',
};

// Sort Options
export const SORT_OPTIONS = {
  RELEVANCE: 'relevance',
  PRICE_LOW_TO_HIGH: 'price_asc',
  PRICE_HIGH_TO_LOW: 'price_desc',
  NEWEST: 'newest',
  OLDEST: 'oldest',
  RATING: 'rating',
  POPULARITY: 'popularity',
  TRENDING: 'trending',
};

// Live Shopping Status
export const LIVE_STATUS = {
  SCHEDULED: 'scheduled',
  LIVE: 'live',
  ENDED: 'ended',
  CANCELLED: 'cancelled',
};

// Review Status
export const REVIEW_STATUS = {
  PENDING: 'pending',
  APPROVED: 'approved',
  REJECTED: 'rejected',
  FLAGGED: 'flagged',
};

// Shipping Methods
export const SHIPPING_METHODS = {
  STANDARD: 'standard',
  EXPRESS: 'express',
  OVERNIGHT: 'overnight',
  PICKUP: 'pickup',
  DIGITAL: 'digital',
};

// Currency Symbols
export const CURRENCY_SYMBOLS = {
  USD: '$',
  EUR: '€',
  GBP: '£',
  CAD: 'C$',
  AUD: 'A$',
  JPY: '¥',
  CNY: '¥',
  INR: '₹',
  BRL: 'R$',
  MXN: '$',
};

// Date Formats
export const DATE_FORMATS = {
  SHORT: 'MM/DD/YYYY',
  LONG: 'MMMM DD, YYYY',
  TIME: 'HH:mm',
  DATETIME: 'MM/DD/YYYY HH:mm',
  ISO: 'YYYY-MM-DDTHH:mm:ss.sssZ',
  RELATIVE: 'relative', // e.g., "2 hours ago"
};

// Validation Patterns
export const VALIDATION_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^\+?[\d\s\-\(\)]+$/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
  USERNAME: /^[a-zA-Z0-9_]{3,20}$/,
  POSTAL_CODE: /^[A-Za-z0-9\s\-]{3,10}$/,
  CREDIT_CARD: /^\d{13,19}$/,
  CVV: /^\d{3,4}$/,
};

// Error Codes
export const ERROR_CODES = {
  // Network Errors
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  CONNECTION_ERROR: 'CONNECTION_ERROR',
  
  // Authentication Errors
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  
  // Validation Errors
  INVALID_EMAIL: 'INVALID_EMAIL',
  INVALID_PASSWORD: 'INVALID_PASSWORD',
  INVALID_PHONE: 'INVALID_PHONE',
  REQUIRED_FIELD: 'REQUIRED_FIELD',
  
  // Business Logic Errors
  PRODUCT_OUT_OF_STOCK: 'PRODUCT_OUT_OF_STOCK',
  INSUFFICIENT_FUNDS: 'INSUFFICIENT_FUNDS',
  CART_EMPTY: 'CART_EMPTY',
  ORDER_NOT_FOUND: 'ORDER_NOT_FOUND',
  
  // Server Errors
  INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  BAD_REQUEST: 'BAD_REQUEST',
  NOT_FOUND: 'NOT_FOUND',
};

// Success Codes
export const SUCCESS_CODES = {
  USER_CREATED: 'USER_CREATED',
  LOGIN_SUCCESS: 'LOGIN_SUCCESS',
  LOGOUT_SUCCESS: 'LOGOUT_SUCCESS',
  ORDER_PLACED: 'ORDER_PLACED',
  PAYMENT_SUCCESS: 'PAYMENT_SUCCESS',
  PROFILE_UPDATED: 'PROFILE_UPDATED',
  REVIEW_SUBMITTED: 'REVIEW_SUBMITTED',
  PRODUCT_ADDED_TO_CART: 'PRODUCT_ADDED_TO_CART',
};

// Storage Keys
export const STORAGE_KEYS = {
  USER_TOKEN: 'user_token',
  USER_DATA: 'user_data',
  CART_DATA: 'cart_data',
  WISHLIST_DATA: 'wishlist_data',
  SEARCH_HISTORY: 'search_history',
  SETTINGS: 'app_settings',
  ONBOARDING_COMPLETED: 'onboarding_completed',
  LAST_APP_VERSION: 'last_app_version',
  NOTIFICATION_SETTINGS: 'notification_settings',
  THEME_PREFERENCE: 'theme_preference',
};

// API Endpoints
export const API_ENDPOINTS = {
  // Authentication
  LOGIN: '/auth/login',
  REGISTER: '/auth/register',
  LOGOUT: '/auth/logout',
  REFRESH_TOKEN: '/auth/refresh',
  FORGOT_PASSWORD: '/auth/forgot-password',
  RESET_PASSWORD: '/auth/reset-password',
  
  // User
  USER_PROFILE: '/user/profile',
  UPDATE_PROFILE: '/user/profile',
  CHANGE_PASSWORD: '/user/change-password',
  DELETE_ACCOUNT: '/user/delete',
  
  // Products
  PRODUCTS: '/products',
  PRODUCT_DETAIL: '/products/:id',
  PRODUCT_REVIEWS: '/products/:id/reviews',
  PRODUCT_SEARCH: '/products/search',
  TRENDING_PRODUCTS: '/products/trending',
  
  // Cart
  CART: '/cart',
  ADD_TO_CART: '/cart/add',
  UPDATE_CART: '/cart/update',
  REMOVE_FROM_CART: '/cart/remove',
  CLEAR_CART: '/cart/clear',
  
  // Orders
  ORDERS: '/orders',
  ORDER_DETAIL: '/orders/:id',
  CREATE_ORDER: '/orders/create',
  CANCEL_ORDER: '/orders/:id/cancel',
  
  // Social
  FOLLOW_USER: '/social/follow',
  UNFOLLOW_USER: '/social/unfollow',
  LIKE_PRODUCT: '/social/like',
  COMMENT_PRODUCT: '/social/comment',
  
  // Live Shopping
  LIVE_EVENTS: '/live/events',
  JOIN_LIVE_EVENT: '/live/join/:id',
  LIVE_CHAT: '/live/chat/:id',
  
  // Notifications
  NOTIFICATIONS: '/notifications',
  MARK_READ: '/notifications/read',
  NOTIFICATION_SETTINGS: '/notifications/settings',
};

// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
};

export default {
  SCREENS,
  USER_ROLES,
  PRODUCT_CATEGORIES,
  ORDER_STATUS,
  PAYMENT_STATUS,
  NOTIFICATION_TYPES,
  MEDIA_TYPES,
  IMAGE_FORMATS,
  VIDEO_FORMATS,
  SOCIAL_ACTIONS,
  CART_ACTIONS,
  SEARCH_FILTERS,
  SORT_OPTIONS,
  LIVE_STATUS,
  REVIEW_STATUS,
  SHIPPING_METHODS,
  CURRENCY_SYMBOLS,
  DATE_FORMATS,
  VALIDATION_PATTERNS,
  ERROR_CODES,
  SUCCESS_CODES,
  STORAGE_KEYS,
  API_ENDPOINTS,
  HTTP_STATUS,
};
