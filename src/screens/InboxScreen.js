import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  SafeAreaView,
  FlatList,
  Alert,
} from 'react-native';
import { colors, dimensions, commonStyles } from '../styles/tiktokStyles';
import { useAuth } from '../context/AuthContext';

const InboxScreen = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('all');
  const [messages, setMessages] = useState([]);
  const [notifications, setNotifications] = useState([]);

  useEffect(() => {
    loadInboxData();
  }, []);

  const loadInboxData = () => {
    // Mock messages data
    setMessages([
      {
        id: '1',
        type: 'message',
        sender: {
          id: 'seller1',
          name: 'Fashion Store',
          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
          verified: true,
        },
        lastMessage: 'Thank you for your order! Your items will be shipped soon 📦',
        timestamp: '2m',
        unread: true,
        isOnline: true,
      },
      {
        id: '2',
        type: 'message',
        sender: {
          id: 'seller2',
          name: 'Tech Hub',
          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
          verified: true,
        },
        lastMessage: 'Hi! Do you have any questions about the wireless earbuds?',
        timestamp: '1h',
        unread: false,
        isOnline: false,
      },
      {
        id: '3',
        type: 'message',
        sender: {
          id: 'support',
          name: 'StoreTok Support',
          avatar: 'https://via.placeholder.com/150',
          verified: true,
        },
        lastMessage: 'Welcome to StoreTok! How can we help you today?',
        timestamp: '1d',
        unread: false,
        isOnline: true,
      },
    ]);

    // Mock notifications data
    setNotifications([
      {
        id: '1',
        type: 'like',
        user: {
          name: 'Sarah Johnson',
          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
        },
        action: 'liked your review',
        product: 'Trendy Sneakers',
        timestamp: '5m',
        unread: true,
      },
      {
        id: '2',
        type: 'follow',
        user: {
          name: 'Mike Chen',
          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
        },
        action: 'started following you',
        timestamp: '1h',
        unread: true,
      },
      {
        id: '3',
        type: 'comment',
        user: {
          name: 'Emma Wilson',
          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
        },
        action: 'commented on your review',
        product: 'Wireless Earbuds',
        comment: 'Great review! Thanks for sharing',
        timestamp: '2h',
        unread: false,
      },
      {
        id: '4',
        type: 'order',
        action: 'Your order has been shipped',
        product: 'Trendy Sneakers',
        timestamp: '1d',
        unread: false,
      },
    ]);
  };

  const TabButton = ({ title, isActive, onPress, count = 0 }) => (
    <TouchableOpacity
      style={{
        flex: 1,
        paddingVertical: dimensions.md,
        borderBottomWidth: 2,
        borderBottomColor: isActive ? colors.red : 'transparent',
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'center',
      }}
      onPress={onPress}
    >
      <Text style={{
        color: isActive ? colors.white : colors.secondaryText,
        fontWeight: isActive ? 'bold' : 'normal',
        fontSize: 16,
        marginRight: count > 0 ? dimensions.xs : 0,
      }}>
        {title}
      </Text>
      {count > 0 && (
        <View style={{
          backgroundColor: colors.red,
          borderRadius: 10,
          minWidth: 20,
          height: 20,
          alignItems: 'center',
          justifyContent: 'center',
          paddingHorizontal: dimensions.xs,
        }}>
          <Text style={{
            color: colors.white,
            fontSize: 10,
            fontWeight: 'bold',
          }}>
            {count > 99 ? '99+' : count}
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );

  const MessageItem = ({ message }) => (
    <TouchableOpacity
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: dimensions.md,
        paddingVertical: dimensions.md,
        backgroundColor: message.unread ? colors.darkGray : 'transparent',
      }}
      onPress={() => Alert.alert('Message', `Open chat with ${message.sender.name}`)}
    >
      {/* Avatar with online indicator */}
      <View style={{ position: 'relative', marginRight: dimensions.md }}>
        <Image
          source={{ uri: message.sender.avatar }}
          style={{
            width: 48,
            height: 48,
            borderRadius: 24,
          }}
        />
        {message.isOnline && (
          <View style={{
            position: 'absolute',
            bottom: 0,
            right: 0,
            backgroundColor: colors.green,
            borderRadius: 8,
            width: 16,
            height: 16,
            borderWidth: 2,
            borderColor: colors.black,
          }} />
        )}
      </View>

      {/* Message content */}
      <View style={{ flex: 1 }}>
        <View style={[commonStyles.row, { alignItems: 'center', marginBottom: dimensions.xs }]}>
          <Text style={[commonStyles.bodyMedium, { fontWeight: 'bold', marginRight: dimensions.xs }]}>
            {message.sender.name}
          </Text>
          {message.sender.verified && (
            <Text style={{ color: colors.blue, fontSize: 12 }}>✓</Text>
          )}
          <Text style={[commonStyles.bodySmall, { color: colors.mutedText, marginLeft: 'auto' }]}>
            {message.timestamp}
          </Text>
        </View>
        
        <Text 
          style={[
            commonStyles.bodyMedium, 
            { 
              color: message.unread ? colors.white : colors.secondaryText,
              fontWeight: message.unread ? '500' : 'normal'
            }
          ]} 
          numberOfLines={2}
        >
          {message.lastMessage}
        </Text>
      </View>

      {/* Unread indicator */}
      {message.unread && (
        <View style={{
          backgroundColor: colors.red,
          borderRadius: 6,
          width: 12,
          height: 12,
          marginLeft: dimensions.sm,
        }} />
      )}
    </TouchableOpacity>
  );

  const NotificationItem = ({ notification }) => {
    const getNotificationIcon = () => {
      switch (notification.type) {
        case 'like': return '❤️';
        case 'follow': return '👤';
        case 'comment': return '💬';
        case 'order': return '📦';
        default: return '🔔';
      }
    };

    return (
      <TouchableOpacity
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          paddingHorizontal: dimensions.md,
          paddingVertical: dimensions.md,
          backgroundColor: notification.unread ? colors.darkGray : 'transparent',
        }}
        onPress={() => Alert.alert('Notification', 'Open notification details')}
      >
        {/* Icon or Avatar */}
        <View style={{
          width: 48,
          height: 48,
          borderRadius: 24,
          backgroundColor: colors.mediumGray,
          alignItems: 'center',
          justifyContent: 'center',
          marginRight: dimensions.md,
        }}>
          {notification.user ? (
            <Image
              source={{ uri: notification.user.avatar }}
              style={{
                width: 48,
                height: 48,
                borderRadius: 24,
              }}
            />
          ) : (
            <Text style={{ fontSize: 24 }}>
              {getNotificationIcon()}
            </Text>
          )}
        </View>

        {/* Notification content */}
        <View style={{ flex: 1 }}>
          <View style={[commonStyles.row, { alignItems: 'center', marginBottom: dimensions.xs }]}>
            <Text style={[commonStyles.bodyMedium, { fontWeight: 'bold', flex: 1 }]}>
              {notification.user ? notification.user.name : 'StoreTok'}
            </Text>
            <Text style={[commonStyles.bodySmall, { color: colors.mutedText }]}>
              {notification.timestamp}
            </Text>
          </View>
          
          <Text style={[commonStyles.bodyMedium, { color: colors.secondaryText, marginBottom: dimensions.xs }]}>
            {notification.action}
            {notification.product && (
              <Text style={{ color: colors.white, fontWeight: 'bold' }}>
                {' '}{notification.product}
              </Text>
            )}
          </Text>

          {notification.comment && (
            <Text style={[commonStyles.bodySmall, { color: colors.mutedText, fontStyle: 'italic' }]}>
              "{notification.comment}"
            </Text>
          )}
        </View>

        {/* Unread indicator */}
        {notification.unread && (
          <View style={{
            backgroundColor: colors.red,
            borderRadius: 6,
            width: 12,
            height: 12,
            marginLeft: dimensions.sm,
          }} />
        )}
      </TouchableOpacity>
    );
  };

  const renderContent = () => {
    if (activeTab === 'messages') {
      return (
        <FlatList
          data={messages}
          renderItem={({ item }) => <MessageItem message={item} />}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
        />
      );
    } else if (activeTab === 'notifications') {
      return (
        <FlatList
          data={notifications}
          renderItem={({ item }) => <NotificationItem notification={item} />}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
        />
      );
    } else {
      // All tab - combine messages and notifications
      const allItems = [
        ...messages.map(m => ({ ...m, itemType: 'message' })),
        ...notifications.map(n => ({ ...n, itemType: 'notification' }))
      ].sort((a, b) => {
        // Sort by timestamp (mock sorting)
        const timeA = a.timestamp.includes('m') ? 1 : a.timestamp.includes('h') ? 60 : 1440;
        const timeB = b.timestamp.includes('m') ? 1 : b.timestamp.includes('h') ? 60 : 1440;
        return timeA - timeB;
      });

      return (
        <FlatList
          data={allItems}
          renderItem={({ item }) => 
            item.itemType === 'message' ? 
              <MessageItem message={item} /> : 
              <NotificationItem notification={item} />
          }
          keyExtractor={(item) => `${item.itemType}-${item.id}`}
          showsVerticalScrollIndicator={false}
        />
      );
    }
  };

  const unreadMessages = messages.filter(m => m.unread).length;
  const unreadNotifications = notifications.filter(n => n.unread).length;

  return (
    <SafeAreaView style={commonStyles.container}>
      {/* Header */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: dimensions.md,
        paddingVertical: dimensions.md,
        borderBottomWidth: 1,
        borderBottomColor: colors.border,
      }}>
        <Text style={[commonStyles.heading2]}>
          Inbox
        </Text>
        
        <TouchableOpacity>
          <Text style={{ fontSize: 24 }}>⚙️</Text>
        </TouchableOpacity>
      </View>

      {/* Tabs */}
      <View style={{
        flexDirection: 'row',
        borderBottomWidth: 1,
        borderBottomColor: colors.border,
      }}>
        <TabButton
          title="All"
          isActive={activeTab === 'all'}
          onPress={() => setActiveTab('all')}
          count={unreadMessages + unreadNotifications}
        />
        <TabButton
          title="Messages"
          isActive={activeTab === 'messages'}
          onPress={() => setActiveTab('messages')}
          count={unreadMessages}
        />
        <TabButton
          title="Notifications"
          isActive={activeTab === 'notifications'}
          onPress={() => setActiveTab('notifications')}
          count={unreadNotifications}
        />
      </View>

      {/* Content */}
      {renderContent()}
    </SafeAreaView>
  );
};

export default InboxScreen;
