import React from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Image,
  SafeAreaView,
  Alert,
} from 'react-native';
import { colors, dimensions, commonStyles } from '../styles/tiktokStyles';
import { useCart } from '../context/CartContext';

const CartScreen = () => {
  const {
    items,
    removeFromCart,
    updateQuantity,
    clearCart,
    getCartTotal,
    getCartItemCount,
  } = useCart();

  const handleQuantityChange = (itemId, newQuantity) => {
    if (newQuantity <= 0) {
      Alert.alert(
        'Remove Item',
        'Are you sure you want to remove this item from your cart?',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Remove', style: 'destructive', onPress: () => removeFromCart(itemId) },
        ]
      );
    } else {
      updateQuantity(itemId, newQuantity);
    }
  };

  const handleCheckout = () => {
    if (items.length === 0) {
      Alert.alert('Empty Cart', 'Your cart is empty. Add some items first!');
      return;
    }

    Alert.alert(
      'Checkout',
      `Total: $${getCartTotal().toFixed(2)}\n\nProceed to checkout?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Checkout',
          onPress: () => {
            Alert.alert('Success!', 'Order placed successfully! 🎉');
            clearCart();
          },
        },
      ]
    );
  };

  const handleClearCart = () => {
    if (items.length === 0) return;

    Alert.alert(
      'Clear Cart',
      'Are you sure you want to remove all items from your cart?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Clear All', style: 'destructive', onPress: clearCart },
      ]
    );
  };

  const CartItem = ({ item }) => (
    <View style={{
      backgroundColor: colors.darkGray,
      borderRadius: dimensions.radiusMd,
      padding: dimensions.md,
      marginVertical: dimensions.sm,
      marginHorizontal: dimensions.md,
      flexDirection: 'row',
      alignItems: 'center',
    }}>
      {/* Product Image */}
      <Image
        source={{ uri: item.image }}
        style={{
          width: 80,
          height: 80,
          borderRadius: dimensions.radiusMd,
          marginRight: dimensions.md,
        }}
        resizeMode="cover"
      />

      {/* Product Info */}
      <View style={{ flex: 1, marginRight: dimensions.md }}>
        <Text style={[commonStyles.bodyLarge, { fontWeight: 'bold', marginBottom: dimensions.xs }]}>
          {item.name}
        </Text>
        
        <Text style={[commonStyles.bodyMedium, { color: colors.red, fontWeight: 'bold' }]}>
          ${item.price.toFixed(2)}
        </Text>
        
        <Text style={[commonStyles.bodySmall, { color: colors.secondaryText, marginTop: dimensions.xs }]}>
          Subtotal: ${(item.price * item.quantity).toFixed(2)}
        </Text>
      </View>

      {/* Quantity Controls */}
      <View style={{ alignItems: 'center' }}>
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          backgroundColor: colors.mediumGray,
          borderRadius: dimensions.radiusMd,
          padding: dimensions.xs,
        }}>
          <TouchableOpacity
            style={{
              width: 32,
              height: 32,
              borderRadius: 16,
              backgroundColor: colors.lightGray,
              alignItems: 'center',
              justifyContent: 'center',
            }}
            onPress={() => handleQuantityChange(item.id, item.quantity - 1)}
          >
            <Text style={{ color: colors.white, fontSize: 18, fontWeight: 'bold' }}>-</Text>
          </TouchableOpacity>

          <Text style={[commonStyles.bodyLarge, { marginHorizontal: dimensions.md, fontWeight: 'bold' }]}>
            {item.quantity}
          </Text>

          <TouchableOpacity
            style={{
              width: 32,
              height: 32,
              borderRadius: 16,
              backgroundColor: colors.red,
              alignItems: 'center',
              justifyContent: 'center',
            }}
            onPress={() => handleQuantityChange(item.id, item.quantity + 1)}
          >
            <Text style={{ color: colors.white, fontSize: 18, fontWeight: 'bold' }}>+</Text>
          </TouchableOpacity>
        </View>

        <TouchableOpacity
          style={{
            marginTop: dimensions.sm,
            padding: dimensions.xs,
          }}
          onPress={() => removeFromCart(item.id)}
        >
          <Text style={{ color: colors.error, fontSize: 12 }}>Remove</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const EmptyCart = () => (
    <View style={[commonStyles.center, { flex: 1, paddingHorizontal: dimensions.lg }]}>
      <Text style={{ fontSize: 64, marginBottom: dimensions.lg }}>🛒</Text>
      <Text style={[commonStyles.heading2, { textAlign: 'center', marginBottom: dimensions.md }]}>
        Your cart is empty
      </Text>
      <Text style={[commonStyles.bodyLarge, { textAlign: 'center', color: colors.secondaryText }]}>
        Start shopping to add items to your cart
      </Text>
    </View>
  );

  return (
    <SafeAreaView style={commonStyles.container}>
      {/* Header */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: dimensions.md,
        paddingVertical: dimensions.md,
        borderBottomWidth: 1,
        borderBottomColor: colors.border,
      }}>
        <Text style={[commonStyles.heading2]}>
          Shopping Cart
        </Text>
        
        {items.length > 0 && (
          <TouchableOpacity onPress={handleClearCart}>
            <Text style={{ color: colors.error, fontSize: 14 }}>Clear All</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Cart Items */}
      {items.length === 0 ? (
        <EmptyCart />
      ) : (
        <>
          <FlatList
            data={items}
            renderItem={({ item }) => <CartItem item={item} />}
            keyExtractor={(item) => item.id}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: dimensions.xl }}
          />

          {/* Cart Summary */}
          <View style={{
            backgroundColor: colors.darkGray,
            padding: dimensions.lg,
            borderTopWidth: 1,
            borderTopColor: colors.border,
          }}>
            <View style={[commonStyles.row, commonStyles.spaceBetween, { marginBottom: dimensions.md }]}>
              <Text style={[commonStyles.bodyLarge]}>
                Items ({getCartItemCount()})
              </Text>
              <Text style={[commonStyles.bodyLarge]}>
                ${getCartTotal().toFixed(2)}
              </Text>
            </View>

            <View style={[commonStyles.row, commonStyles.spaceBetween, { marginBottom: dimensions.md }]}>
              <Text style={[commonStyles.bodyMedium, { color: colors.secondaryText }]}>
                Shipping
              </Text>
              <Text style={[commonStyles.bodyMedium, { color: colors.green }]}>
                FREE
              </Text>
            </View>

            <View style={{
              height: 1,
              backgroundColor: colors.border,
              marginVertical: dimensions.md,
            }} />

            <View style={[commonStyles.row, commonStyles.spaceBetween, { marginBottom: dimensions.lg }]}>
              <Text style={[commonStyles.heading3]}>
                Total
              </Text>
              <Text style={[commonStyles.heading3, { color: colors.red }]}>
                ${getCartTotal().toFixed(2)}
              </Text>
            </View>

            <TouchableOpacity
              style={[commonStyles.primaryButton, { marginBottom: dimensions.sm }]}
              onPress={handleCheckout}
            >
              <Text style={[commonStyles.bodyLarge, { color: colors.white, fontWeight: 'bold' }]}>
                Checkout 🚀
              </Text>
            </TouchableOpacity>

            <Text style={[commonStyles.bodySmall, { color: colors.secondaryText, textAlign: 'center' }]}>
              Secure checkout with 256-bit SSL encryption
            </Text>
          </View>
        </>
      )}
    </SafeAreaView>
  );
};

export default CartScreen;
