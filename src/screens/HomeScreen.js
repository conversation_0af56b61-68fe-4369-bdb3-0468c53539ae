import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  Dimensions,
  TouchableOpacity,
  Image,
  SafeAreaView,
  StatusBar,
  Animated,
} from 'react-native';
import { colors, dimensions, commonStyles } from '../styles/tiktokStyles';
import { firestore } from '../services/firebase';
import { useCart } from '../context/CartContext';
import ProductCard from '../components/ProductCard';
import CommentSection from '../components/CommentSection';
import TraderScreen from '../screens/TraderScreen';

const { height: screenHeight } = Dimensions.get('window');

const HomeScreen = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [showComments, setShowComments] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [showTrader, setShowTrader] = useState(false);
  const [selectedTraderId, setSelectedTraderId] = useState(null);
  const scrollViewRef = useRef(null);
  const productRefs = useRef([]);
  const { addToCart, isInCart } = useCart();

  useEffect(() => {
    loadProducts();
  }, []);

  // Intersection Observer for auto-play (TikTok-style)
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.8, // Adjust this value to change the scroll trigger point
    };

    // This function handles the intersection of products
    const handleIntersection = (entries) => {
      entries.forEach((entry) => {
        const index = parseInt(entry.target.dataset.index);
        if (entry.isIntersecting) {
          setCurrentIndex(index);
          // Auto-play video if it exists
          const videoElement = entry.target.querySelector('video');
          if (videoElement) {
            videoElement.play();
          }
        } else {
          // Pause video when not visible
          const videoElement = entry.target.querySelector('video');
          if (videoElement) {
            videoElement.pause();
          }
        }
      });
    };

    const observer = new IntersectionObserver(handleIntersection, observerOptions);

    // Observe each product reference
    productRefs.current.forEach((productRef) => {
      if (productRef) {
        observer.observe(productRef);
      }
    });

    // Cleanup observer when component unmounts
    return () => {
      observer.disconnect();
    };
  }, [products]);

  const loadProducts = async () => {
    try {
      setLoading(true);
      const productsSnapshot = await firestore.collection('products').get();
      const productsData = productsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      }));
      setProducts(productsData);
    } catch (error) {
      console.error('Error loading products:', error);
    } finally {
      setLoading(false);
    }
  };

  // Handle product reference for intersection observer
  const handleProductRef = (index) => (ref) => {
    productRefs.current[index] = ref;
  };

  const handleLike = async (productId) => {
    try {
      // Update local state immediately for better UX
      setProducts(prevProducts =>
        prevProducts.map(product =>
          product.id === productId
            ? { ...product, likes: product.likes + 1, isLiked: !product.isLiked }
            : product
        )
      );

      // Update in Firebase (mock)
      const productRef = firestore.collection('products').doc(productId);
      const productDoc = await productRef.get();
      if (productDoc.exists) {
        const currentLikes = productDoc.data().likes || 0;
        await productRef.update({
          likes: currentLikes + 1,
        });
      }
    } catch (error) {
      console.error('Error liking product:', error);
    }
  };

  const handleAddToCart = (product) => {
    addToCart(product);
  };

  const handleShare = (product) => {
    // Implement share functionality
    console.log('Sharing product:', product.name);
  };

  const handleComment = (product) => {
    setSelectedProduct(product);
    setShowComments(true);
  };

  const handleTraderPress = (traderId) => {
    setSelectedTraderId(traderId);
    setShowTrader(true);
  };

  const handleSwipeLeft = () => {
    if (currentIndex < products.length - 1) {
      const currentProduct = products[currentIndex];
      if (currentProduct?.sellerId) {
        handleTraderPress(currentProduct.sellerId);
      }
    }
  };



  if (loading) {
    return (
      <View style={[commonStyles.container, commonStyles.center]}>
        <Text style={commonStyles.bodyLarge}>Loading amazing products...</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={commonStyles.container}>
      <StatusBar barStyle="light-content" backgroundColor={colors.black} />
      
      {/* Top Navigation - TikTok Style */}
      <View style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        zIndex: 10,
        paddingTop: StatusBar.currentHeight || dimensions.lg,
        paddingHorizontal: dimensions.md,
        paddingBottom: dimensions.sm,
        backgroundColor: 'rgba(0, 0, 0, 0.3)',
      }}>
        <View style={[commonStyles.row, commonStyles.spaceBetween, { marginBottom: dimensions.sm }]}>
          <TouchableOpacity>
            <Text style={{
              fontSize: 18,
              color: colors.white,
              fontWeight: 'bold',
            }}>
              Following
            </Text>
          </TouchableOpacity>

          <Text style={{
            fontSize: 20,
            color: colors.white,
            fontWeight: 'bold',
          }}>
            StoreTok
          </Text>

          <TouchableOpacity>
            <Text style={{
              fontSize: 18,
              color: colors.white,
              fontWeight: 'bold',
            }}>
              For You
            </Text>
          </TouchableOpacity>
        </View>

        {/* Quick Actions */}
        <View style={[commonStyles.row, commonStyles.spaceBetween]}>
          <TouchableOpacity style={{
            backgroundColor: 'rgba(255, 255, 255, 0.2)',
            borderRadius: 20,
            paddingHorizontal: dimensions.sm,
            paddingVertical: 4,
          }}>
            <Text style={{ color: colors.white, fontSize: 12 }}>🔥 Trending</Text>
          </TouchableOpacity>

          <TouchableOpacity style={{
            backgroundColor: 'rgba(255, 255, 255, 0.2)',
            borderRadius: 20,
            paddingHorizontal: dimensions.sm,
            paddingVertical: 4,
          }}>
            <Text style={{ color: colors.white, fontSize: 12 }}>🎯 For You</Text>
          </TouchableOpacity>

          <TouchableOpacity style={{
            backgroundColor: 'rgba(255, 255, 255, 0.2)',
            borderRadius: 20,
            paddingHorizontal: dimensions.sm,
            paddingVertical: 4,
          }}>
            <Text style={{ color: colors.white, fontSize: 12 }}>🛍️ Shopping</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* TikTok-Style Vertical Scrolling Feed */}
      <ScrollView
        ref={scrollViewRef}
        pagingEnabled
        showsVerticalScrollIndicator={false}
        snapToInterval={screenHeight}
        snapToAlignment="start"
        decelerationRate="fast"
        style={{ flex: 1 }}
      >
        {products.map((product, index) => (
          <View
            key={product.id}
            ref={handleProductRef(index)}
            data-index={index}
            style={{
              height: screenHeight,
              width: screenWidth,
            }}
          >
            <ProductCard
              product={product}
              isActive={index === currentIndex}
              onLike={() => handleLike(product.id)}
              onAddToCart={() => handleAddToCart(product)}
              onShare={() => handleShare(product)}
              onComment={() => handleComment(product)}
              onTraderPress={() => handleTraderPress(product.sellerId)}
              onSwipeLeft={handleSwipeLeft}
              isInCart={isInCart(product.id)}
            />
          </View>
        ))}
      </ScrollView>

      {/* Side indicators */}
      <View style={{
        position: 'absolute',
        right: dimensions.md,
        top: '50%',
        transform: [{ translateY: -50 }],
      }}>
        {products.map((_, index) => (
          <View
            key={index}
            style={{
              width: 4,
              height: 20,
              backgroundColor: index === currentIndex ? colors.red : colors.secondaryText,
              marginVertical: 2,
              borderRadius: 2,
            }}
          />
        ))}
      </View>

      {/* Swipe indicator */}
      <View style={{
        position: 'absolute',
        left: dimensions.md,
        top: '50%',
        transform: [{ translateY: -50 }],
        opacity: 0.7,
      }}>
        <Text style={{
          color: colors.white,
          fontSize: 12,
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          paddingHorizontal: dimensions.sm,
          paddingVertical: dimensions.xs,
          borderRadius: dimensions.radiusSm,
        }}>
          ← Swipe for trader
        </Text>
      </View>

      {/* Comment Section Overlay */}
      {showComments && selectedProduct && (
        <CommentSection
          productId={selectedProduct.id}
          isVisible={showComments}
          onClose={() => {
            setShowComments(false);
            setSelectedProduct(null);
          }}
        />
      )}

      {/* Trader Screen Overlay */}
      {showTrader && selectedTraderId && (
        <TraderScreen
          traderId={selectedTraderId}
          onClose={() => {
            setShowTrader(false);
            setSelectedTraderId(null);
          }}
        />
      )}
    </SafeAreaView>
  );
};

export default HomeScreen;
