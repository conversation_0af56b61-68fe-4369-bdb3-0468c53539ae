import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  FlatList,
  Dimensions,
  TouchableOpacity,
  Image,
  SafeAreaView,
  StatusBar,
  Animated,
  PanGestureHandler,
} from 'react-native';
import { colors, dimensions, commonStyles } from '../styles/tiktokStyles';
import { firestore } from '../services/firebase';
import { useCart } from '../context/CartContext';
import ProductCard from '../components/ProductCard';
import CommentSection from '../components/CommentSection';
import TraderScreen from '../screens/TraderScreen';

const { height: screenHeight } = Dimensions.get('window');

const HomeScreen = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [showComments, setShowComments] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [showTrader, setShowTrader] = useState(false);
  const [selectedTraderId, setSelectedTraderId] = useState(null);
  const flatListRef = useRef(null);
  const { addToCart, isInCart } = useCart();
  const translateX = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    loadProducts();
  }, []);

  const loadProducts = async () => {
    try {
      setLoading(true);
      const productsSnapshot = await firestore.collection('products').get();
      const productsData = productsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      }));
      setProducts(productsData);
    } catch (error) {
      console.error('Error loading products:', error);
    } finally {
      setLoading(false);
    }
  };

  const onViewableItemsChanged = useRef(({ viewableItems }) => {
    if (viewableItems.length > 0) {
      setCurrentIndex(viewableItems[0].index);
    }
  }).current;

  const viewabilityConfig = useRef({
    itemVisiblePercentThreshold: 50,
  }).current;

  const handleLike = async (productId) => {
    try {
      // Update local state immediately for better UX
      setProducts(prevProducts =>
        prevProducts.map(product =>
          product.id === productId
            ? { ...product, likes: product.likes + 1, isLiked: !product.isLiked }
            : product
        )
      );

      // Update in Firebase (mock)
      const productRef = firestore.collection('products').doc(productId);
      const productDoc = await productRef.get();
      if (productDoc.exists) {
        const currentLikes = productDoc.data().likes || 0;
        await productRef.update({
          likes: currentLikes + 1,
        });
      }
    } catch (error) {
      console.error('Error liking product:', error);
    }
  };

  const handleAddToCart = (product) => {
    addToCart(product);
  };

  const handleShare = (product) => {
    // Implement share functionality
    console.log('Sharing product:', product.name);
  };

  const handleComment = (product) => {
    setSelectedProduct(product);
    setShowComments(true);
  };

  const handleTraderPress = (traderId) => {
    setSelectedTraderId(traderId);
    setShowTrader(true);
  };

  const handleSwipeLeft = () => {
    if (currentIndex < products.length - 1) {
      const currentProduct = products[currentIndex];
      if (currentProduct?.sellerId) {
        handleTraderPress(currentProduct.sellerId);
      }
    }
  };

  const renderProduct = ({ item, index }) => (
    <ProductCard
      product={item}
      isActive={index === currentIndex}
      onLike={() => handleLike(item.id)}
      onAddToCart={() => handleAddToCart(item)}
      onShare={() => handleShare(item)}
      onComment={() => handleComment(item)}
      onTraderPress={() => handleTraderPress(item.sellerId)}
      onSwipeLeft={handleSwipeLeft}
      isInCart={isInCart(item.id)}
    />
  );

  if (loading) {
    return (
      <View style={[commonStyles.container, commonStyles.center]}>
        <Text style={commonStyles.bodyLarge}>Loading amazing products...</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={commonStyles.container}>
      <StatusBar barStyle="light-content" backgroundColor={colors.black} />
      
      {/* Header */}
      <View style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        zIndex: 10,
        paddingTop: StatusBar.currentHeight || dimensions.lg,
        paddingHorizontal: dimensions.md,
        paddingBottom: dimensions.sm,
      }}>
        <View style={[commonStyles.row, commonStyles.spaceBetween]}>
          <TouchableOpacity>
            <Text style={{
              fontSize: 18,
              color: colors.white,
              fontWeight: 'bold',
            }}>
              Following
            </Text>
          </TouchableOpacity>
          
          <Text style={{
            fontSize: 20,
            color: colors.white,
            fontWeight: 'bold',
          }}>
            StoreTok
          </Text>
          
          <TouchableOpacity>
            <Text style={{
              fontSize: 18,
              color: colors.white,
              fontWeight: 'bold',
            }}>
              For You
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Products Feed */}
      <FlatList
        ref={flatListRef}
        data={products}
        renderItem={renderProduct}
        keyExtractor={(item) => item.id}
        pagingEnabled
        showsVerticalScrollIndicator={false}
        snapToInterval={screenHeight}
        snapToAlignment="start"
        decelerationRate="fast"
        onViewableItemsChanged={onViewableItemsChanged}
        viewabilityConfig={viewabilityConfig}
        getItemLayout={(data, index) => ({
          length: screenHeight,
          offset: screenHeight * index,
          index,
        })}
        style={{ flex: 1 }}
      />

      {/* Side indicators */}
      <View style={{
        position: 'absolute',
        right: dimensions.md,
        top: '50%',
        transform: [{ translateY: -50 }],
      }}>
        {products.map((_, index) => (
          <View
            key={index}
            style={{
              width: 4,
              height: 20,
              backgroundColor: index === currentIndex ? colors.red : colors.secondaryText,
              marginVertical: 2,
              borderRadius: 2,
            }}
          />
        ))}
      </View>
    </SafeAreaView>
  );
};

export default HomeScreen;
