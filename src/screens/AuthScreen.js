import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { colors, dimensions, commonStyles } from '../styles/tiktokStyles';
import { useAuth } from '../context/AuthContext';

const AuthScreen = () => {
  const [isLogin, setIsLogin] = useState(true);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [displayName, setDisplayName] = useState('');
  const [loading, setLoading] = useState(false);
  
  const { signIn, signUp, error, clearError } = useAuth();

  const handleSubmit = async () => {
    if (!email || !password) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    if (!isLogin && !displayName) {
      Alert.alert('Error', 'Please enter your display name');
      return;
    }

    try {
      setLoading(true);
      clearError();

      if (isLogin) {
        await signIn(email, password);
      } else {
        await signUp(email, password, displayName);
      }
    } catch (error) {
      Alert.alert('Error', error.message);
    } finally {
      setLoading(false);
    }
  };

  const toggleMode = () => {
    setIsLogin(!isLogin);
    clearError();
    setEmail('');
    setPassword('');
    setDisplayName('');
  };

  return (
    <SafeAreaView style={commonStyles.container}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={{
          flex: 1,
          justifyContent: 'center',
          paddingHorizontal: dimensions.lg,
        }}>
          {/* Logo/Title */}
          <View style={{
            alignItems: 'center',
            marginBottom: dimensions['3xl'],
          }}>
            <Text style={{
              fontSize: 48,
              fontWeight: 'bold',
              color: colors.white,
              marginBottom: dimensions.sm,
            }}>
              StoreTok
            </Text>
            <Text style={[commonStyles.bodyLarge, { color: colors.secondaryText }]}>
              {isLogin ? 'Welcome back!' : 'Join the shopping revolution'}
            </Text>
          </View>

          {/* Form */}
          <View style={{ marginBottom: dimensions.xl }}>
            {!isLogin && (
              <View style={{ marginBottom: dimensions.md }}>
                <Text style={[commonStyles.bodyMedium, { marginBottom: dimensions.sm }]}>
                  Display Name
                </Text>
                <TextInput
                  style={commonStyles.input}
                  placeholder="Enter your display name"
                  placeholderTextColor={colors.mutedText}
                  value={displayName}
                  onChangeText={setDisplayName}
                  autoCapitalize="words"
                />
              </View>
            )}

            <View style={{ marginBottom: dimensions.md }}>
              <Text style={[commonStyles.bodyMedium, { marginBottom: dimensions.sm }]}>
                Email
              </Text>
              <TextInput
                style={commonStyles.input}
                placeholder="Enter your email"
                placeholderTextColor={colors.mutedText}
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>

            <View style={{ marginBottom: dimensions.lg }}>
              <Text style={[commonStyles.bodyMedium, { marginBottom: dimensions.sm }]}>
                Password
              </Text>
              <TextInput
                style={commonStyles.input}
                placeholder="Enter your password"
                placeholderTextColor={colors.mutedText}
                value={password}
                onChangeText={setPassword}
                secureTextEntry
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>

            {error && (
              <View style={{
                backgroundColor: colors.error,
                padding: dimensions.md,
                borderRadius: dimensions.radiusMd,
                marginBottom: dimensions.md,
              }}>
                <Text style={[commonStyles.bodyMedium, { color: colors.white }]}>
                  {error}
                </Text>
              </View>
            )}

            <TouchableOpacity
              style={[
                commonStyles.primaryButton,
                {
                  opacity: loading ? 0.7 : 1,
                  marginBottom: dimensions.md,
                }
              ]}
              onPress={handleSubmit}
              disabled={loading}
            >
              <Text style={[commonStyles.bodyLarge, { color: colors.white, fontWeight: 'bold' }]}>
                {loading ? 'Please wait...' : (isLogin ? 'Sign In' : 'Sign Up')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={commonStyles.ghostButton}
              onPress={toggleMode}
            >
              <Text style={[commonStyles.bodyMedium, { color: colors.secondaryText }]}>
                {isLogin ? "Don't have an account? " : "Already have an account? "}
                <Text style={{ color: colors.red, fontWeight: 'bold' }}>
                  {isLogin ? 'Sign Up' : 'Sign In'}
                </Text>
              </Text>
            </TouchableOpacity>
          </View>

          {/* Demo credentials */}
          <View style={{
            backgroundColor: colors.darkGray,
            padding: dimensions.md,
            borderRadius: dimensions.radiusMd,
            marginTop: dimensions.lg,
          }}>
            <Text style={[commonStyles.bodySmall, { color: colors.secondaryText, textAlign: 'center' }]}>
              Demo Credentials:{'\n'}
              Email: <EMAIL>{'\n'}
              Password: demo123
            </Text>
          </View>

          {/* Social login buttons */}
          <View style={{
            marginTop: dimensions.xl,
            alignItems: 'center',
          }}>
            <Text style={[commonStyles.bodySmall, { color: colors.mutedText, marginBottom: dimensions.md }]}>
              Or continue with
            </Text>
            
            <View style={[commonStyles.row, { justifyContent: 'center' }]}>
              <TouchableOpacity
                style={{
                  backgroundColor: colors.darkGray,
                  padding: dimensions.md,
                  borderRadius: dimensions.radiusFull,
                  marginHorizontal: dimensions.sm,
                  width: 56,
                  height: 56,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Text style={{ fontSize: 24 }}>📱</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={{
                  backgroundColor: colors.darkGray,
                  padding: dimensions.md,
                  borderRadius: dimensions.radiusFull,
                  marginHorizontal: dimensions.sm,
                  width: 56,
                  height: 56,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Text style={{ fontSize: 24 }}>🔍</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default AuthScreen;
