import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  SafeAreaView,
  ScrollView,
  Alert,
} from 'react-native';
import { colors, dimensions, commonStyles } from '../styles/tiktokStyles';
import { useAuth } from '../context/AuthContext';
import { useCart } from '../context/CartContext';

const ProfileScreen = () => {
  const { user, signOut } = useAuth();
  const { getCartItemCount } = useCart();
  const [activeTab, setActiveTab] = useState('posts');

  const handleSignOut = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Sign Out', style: 'destructive', onPress: signOut },
      ]
    );
  };

  const ProfileStat = ({ number, label }) => (
    <View style={{ alignItems: 'center', flex: 1 }}>
      <Text style={[commonStyles.heading2, { color: colors.white }]}>
        {number}
      </Text>
      <Text style={[commonStyles.bodySmall, { color: colors.secondaryText }]}>
        {label}
      </Text>
    </View>
  );

  const TabButton = ({ title, isActive, onPress }) => (
    <TouchableOpacity
      style={{
        flex: 1,
        paddingVertical: dimensions.md,
        borderBottomWidth: 2,
        borderBottomColor: isActive ? colors.red : 'transparent',
        alignItems: 'center',
      }}
      onPress={onPress}
    >
      <Text style={{
        color: isActive ? colors.white : colors.secondaryText,
        fontWeight: isActive ? 'bold' : 'normal',
        fontSize: 16,
      }}>
        {title}
      </Text>
    </TouchableOpacity>
  );

  const MenuOption = ({ icon, title, subtitle, onPress, showArrow = true }) => (
    <TouchableOpacity
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: dimensions.md,
        paddingHorizontal: dimensions.md,
        backgroundColor: colors.darkGray,
        marginVertical: dimensions.xs,
        borderRadius: dimensions.radiusMd,
      }}
      onPress={onPress}
    >
      <Text style={{ fontSize: 24, marginRight: dimensions.md }}>
        {icon}
      </Text>
      
      <View style={{ flex: 1 }}>
        <Text style={[commonStyles.bodyLarge, { fontWeight: '600' }]}>
          {title}
        </Text>
        {subtitle && (
          <Text style={[commonStyles.bodySmall, { color: colors.secondaryText }]}>
            {subtitle}
          </Text>
        )}
      </View>
      
      {showArrow && (
        <Text style={{ color: colors.secondaryText, fontSize: 16 }}>
          →
        </Text>
      )}
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={commonStyles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          paddingHorizontal: dimensions.md,
          paddingVertical: dimensions.md,
        }}>
          <Text style={[commonStyles.heading2]}>
            Profile
          </Text>
          
          <TouchableOpacity onPress={handleSignOut}>
            <Text style={{ fontSize: 24 }}>⚙️</Text>
          </TouchableOpacity>
        </View>

        {/* Profile Info */}
        <View style={{
          alignItems: 'center',
          paddingHorizontal: dimensions.md,
          paddingVertical: dimensions.lg,
        }}>
          {/* Avatar */}
          <View style={{ position: 'relative', marginBottom: dimensions.md }}>
            <Image
              source={{ 
                uri: user?.photoURL || 'https://via.placeholder.com/120'
              }}
              style={{
                width: 120,
                height: 120,
                borderRadius: 60,
                borderWidth: 3,
                borderColor: colors.red,
              }}
            />
            
            {/* Verified badge */}
            <View style={{
              position: 'absolute',
              bottom: 0,
              right: 0,
              backgroundColor: colors.blue,
              borderRadius: 15,
              width: 30,
              height: 30,
              alignItems: 'center',
              justifyContent: 'center',
              borderWidth: 3,
              borderColor: colors.black,
            }}>
              <Text style={{ color: colors.white, fontSize: 16 }}>✓</Text>
            </View>
          </View>

          {/* User Info */}
          <Text style={[commonStyles.heading2, { marginBottom: dimensions.xs }]}>
            @{user?.displayName || user?.email?.split('@')[0] || 'user'}
          </Text>
          
          <Text style={[commonStyles.bodyMedium, { color: colors.secondaryText, marginBottom: dimensions.md }]}>
            {user?.email}
          </Text>

          <Text style={[commonStyles.bodyMedium, { textAlign: 'center', marginBottom: dimensions.lg }]}>
            🛍️ Shopping enthusiast | 💎 Premium member | 🌟 Trendsetter
          </Text>

          {/* Stats */}
          <View style={{
            flexDirection: 'row',
            width: '100%',
            marginBottom: dimensions.lg,
          }}>
            <ProfileStat number="127" label="Following" />
            <ProfileStat number="1.2M" label="Followers" />
            <ProfileStat number="89" label="Likes" />
          </View>

          {/* Action Buttons */}
          <View style={{
            flexDirection: 'row',
            width: '100%',
            marginBottom: dimensions.lg,
          }}>
            <TouchableOpacity
              style={[
                commonStyles.primaryButton,
                { flex: 1, marginRight: dimensions.sm }
              ]}
            >
              <Text style={[commonStyles.bodyMedium, { color: colors.white, fontWeight: 'bold' }]}>
                Edit Profile
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                commonStyles.secondaryButton,
                { flex: 1, marginLeft: dimensions.sm }
              ]}
            >
              <Text style={[commonStyles.bodyMedium, { color: colors.white, fontWeight: 'bold' }]}>
                Share Profile
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Tabs */}
        <View style={{
          flexDirection: 'row',
          borderBottomWidth: 1,
          borderBottomColor: colors.border,
          marginHorizontal: dimensions.md,
        }}>
          <TabButton
            title="Posts"
            isActive={activeTab === 'posts'}
            onPress={() => setActiveTab('posts')}
          />
          <TabButton
            title="Liked"
            isActive={activeTab === 'liked'}
            onPress={() => setActiveTab('liked')}
          />
          <TabButton
            title="Saved"
            isActive={activeTab === 'saved'}
            onPress={() => setActiveTab('saved')}
          />
        </View>

        {/* Content Grid */}
        <View style={{
          flexDirection: 'row',
          flexWrap: 'wrap',
          paddingHorizontal: dimensions.md,
          paddingTop: dimensions.md,
          marginBottom: dimensions.lg,
        }}>
          {[1, 2, 3, 4, 5, 6].map((item) => (
            <TouchableOpacity
              key={item}
              style={{
                width: '32%',
                aspectRatio: 1,
                backgroundColor: colors.darkGray,
                marginRight: '2%',
                marginBottom: '2%',
                borderRadius: dimensions.radiusMd,
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Text style={{ fontSize: 32 }}>📱</Text>
              <Text style={[commonStyles.bodySmall, { color: colors.secondaryText }]}>
                {item}K views
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Menu Options */}
        <View style={{ paddingHorizontal: dimensions.md, marginBottom: dimensions.xl }}>
          <Text style={[commonStyles.heading3, { marginBottom: dimensions.md }]}>
            Account
          </Text>

          <MenuOption
            icon="🛒"
            title="My Orders"
            subtitle="Track your purchases"
            onPress={() => Alert.alert('Orders', 'Orders screen coming soon!')}
          />

          <MenuOption
            icon="❤️"
            title="Wishlist"
            subtitle="Your saved items"
            onPress={() => Alert.alert('Wishlist', 'Wishlist screen coming soon!')}
          />

          <MenuOption
            icon="🎯"
            title="Recommendations"
            subtitle="Personalized for you"
            onPress={() => Alert.alert('Recommendations', 'Recommendations coming soon!')}
          />

          <MenuOption
            icon="💳"
            title="Payment Methods"
            subtitle="Manage your cards"
            onPress={() => Alert.alert('Payment', 'Payment methods coming soon!')}
          />

          <MenuOption
            icon="📍"
            title="Addresses"
            subtitle="Shipping addresses"
            onPress={() => Alert.alert('Addresses', 'Address management coming soon!')}
          />

          <MenuOption
            icon="🔔"
            title="Notifications"
            subtitle="Manage notifications"
            onPress={() => Alert.alert('Notifications', 'Notification settings coming soon!')}
          />

          <MenuOption
            icon="🛡️"
            title="Privacy & Security"
            subtitle="Account protection"
            onPress={() => Alert.alert('Privacy', 'Privacy settings coming soon!')}
          />

          <MenuOption
            icon="❓"
            title="Help & Support"
            subtitle="Get assistance"
            onPress={() => Alert.alert('Help', 'Support center coming soon!')}
          />

          <MenuOption
            icon="🚪"
            title="Sign Out"
            subtitle="Log out of your account"
            onPress={handleSignOut}
            showArrow={false}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default ProfileScreen;
