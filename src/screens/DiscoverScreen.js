import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  SafeAreaView,
  TextInput,
  FlatList,
  Dimensions,
} from 'react-native';
import { colors, dimensions, commonStyles } from '../styles/tiktokStyles';
import { firestore } from '../services/firebase';

const { width: screenWidth } = Dimensions.get('window');

const DiscoverScreen = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [trendingHashtags, setTrendingHashtags] = useState([]);
  const [categories, setCategories] = useState([]);
  const [featuredProducts, setFeaturedProducts] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDiscoverData();
  }, []);

  const loadDiscoverData = async () => {
    try {
      setLoading(true);
      
      // Mock trending hashtags
      setTrendingHashtags([
        { id: 1, tag: '#trending', count: '2.1M', color: colors.red },
        { id: 2, tag: '#fashion', count: '1.8M', color: colors.blue },
        { id: 3, tag: '#tech', count: '1.5M', color: colors.green },
        { id: 4, tag: '#lifestyle', count: '1.2M', color: colors.purple },
        { id: 5, tag: '#beauty', count: '980K', color: colors.pink },
        { id: 6, tag: '#fitness', count: '750K', color: colors.yellow },
      ]);

      // Mock categories
      setCategories([
        { id: 1, name: 'Electronics', icon: '📱', count: 1250 },
        { id: 2, name: 'Fashion', icon: '👗', count: 2100 },
        { id: 3, name: 'Home & Garden', icon: '🏠', count: 890 },
        { id: 4, name: 'Sports', icon: '⚽', count: 650 },
        { id: 5, name: 'Beauty', icon: '💄', count: 1100 },
        { id: 6, name: 'Books', icon: '📚', count: 420 },
        { id: 7, name: 'Toys', icon: '🧸', count: 380 },
        { id: 8, name: 'Food', icon: '🍕', count: 720 },
      ]);

      // Load featured products
      const productsSnapshot = await firestore.collection('products').get();
      const productsData = productsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      }));
      setFeaturedProducts(productsData);
      
    } catch (error) {
      console.error('Error loading discover data:', error);
    } finally {
      setLoading(false);
    }
  };

  const HashtagCard = ({ hashtag }) => (
    <TouchableOpacity
      style={{
        backgroundColor: colors.darkGray,
        borderRadius: dimensions.radiusMd,
        padding: dimensions.md,
        marginRight: dimensions.sm,
        minWidth: 120,
        alignItems: 'center',
        borderLeftWidth: 4,
        borderLeftColor: hashtag.color,
      }}
    >
      <Text style={[commonStyles.bodyLarge, { fontWeight: 'bold', marginBottom: dimensions.xs }]}>
        {hashtag.tag}
      </Text>
      <Text style={[commonStyles.bodySmall, { color: colors.secondaryText }]}>
        {hashtag.count} posts
      </Text>
    </TouchableOpacity>
  );

  const CategoryCard = ({ category }) => (
    <TouchableOpacity
      style={{
        backgroundColor: colors.darkGray,
        borderRadius: dimensions.radiusMd,
        padding: dimensions.md,
        margin: dimensions.xs,
        width: (screenWidth - dimensions.md * 3) / 2,
        alignItems: 'center',
      }}
    >
      <Text style={{ fontSize: 32, marginBottom: dimensions.sm }}>
        {category.icon}
      </Text>
      <Text style={[commonStyles.bodyMedium, { fontWeight: 'bold', marginBottom: dimensions.xs }]}>
        {category.name}
      </Text>
      <Text style={[commonStyles.bodySmall, { color: colors.secondaryText }]}>
        {category.count} items
      </Text>
    </TouchableOpacity>
  );

  const ProductGridItem = ({ product }) => (
    <TouchableOpacity
      style={{
        width: (screenWidth - dimensions.md * 3) / 2,
        marginRight: dimensions.sm,
        marginBottom: dimensions.sm,
        backgroundColor: colors.darkGray,
        borderRadius: dimensions.radiusMd,
        overflow: 'hidden',
      }}
    >
      <Image
        source={{ uri: product.images?.[0] }}
        style={{
          width: '100%',
          height: 150,
        }}
        resizeMode="cover"
      />
      
      <View style={{ padding: dimensions.sm }}>
        <Text style={[commonStyles.bodyMedium, { fontWeight: 'bold', marginBottom: dimensions.xs }]} numberOfLines={2}>
          {product.name}
        </Text>
        
        <Text style={[commonStyles.bodyLarge, { color: colors.red, fontWeight: 'bold' }]}>
          ${product.price}
        </Text>
        
        <View style={[commonStyles.row, { marginTop: dimensions.xs }]}>
          <Text style={[commonStyles.bodySmall, { color: colors.secondaryText }]}>
            ❤️ {product.likes}
          </Text>
          <Text style={[commonStyles.bodySmall, { color: colors.secondaryText, marginLeft: dimensions.sm }]}>
            💬 {product.comments}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={[commonStyles.container, commonStyles.center]}>
        <Text style={commonStyles.bodyLarge}>Discovering amazing products...</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={commonStyles.container}>
      {/* Header */}
      <View style={{
        paddingHorizontal: dimensions.md,
        paddingVertical: dimensions.md,
        borderBottomWidth: 1,
        borderBottomColor: colors.border,
      }}>
        <Text style={[commonStyles.heading2, { marginBottom: dimensions.md }]}>
          Discover
        </Text>
        
        {/* Search Bar */}
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          backgroundColor: colors.darkGray,
          borderRadius: dimensions.radiusMd,
          paddingHorizontal: dimensions.md,
          paddingVertical: dimensions.sm,
        }}>
          <Text style={{ fontSize: 16, marginRight: dimensions.sm }}>🔍</Text>
          <TextInput
            style={[commonStyles.bodyMedium, { flex: 1, color: colors.white }]}
            placeholder="Search products, hashtags, users..."
            placeholderTextColor={colors.mutedText}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Trending Hashtags */}
        <View style={{ paddingVertical: dimensions.md }}>
          <Text style={[commonStyles.heading3, { paddingHorizontal: dimensions.md, marginBottom: dimensions.md }]}>
            Trending Hashtags
          </Text>
          
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{ paddingHorizontal: dimensions.md }}
          >
            {trendingHashtags.map((hashtag) => (
              <HashtagCard key={hashtag.id} hashtag={hashtag} />
            ))}
          </ScrollView>
        </View>

        {/* Categories */}
        <View style={{ paddingVertical: dimensions.md }}>
          <Text style={[commonStyles.heading3, { paddingHorizontal: dimensions.md, marginBottom: dimensions.md }]}>
            Shop by Category
          </Text>
          
          <View style={{
            flexDirection: 'row',
            flexWrap: 'wrap',
            paddingHorizontal: dimensions.sm,
          }}>
            {categories.map((category) => (
              <CategoryCard key={category.id} category={category} />
            ))}
          </View>
        </View>

        {/* Featured Products */}
        <View style={{ paddingVertical: dimensions.md }}>
          <Text style={[commonStyles.heading3, { paddingHorizontal: dimensions.md, marginBottom: dimensions.md }]}>
            Featured Products
          </Text>
          
          <View style={{
            flexDirection: 'row',
            flexWrap: 'wrap',
            paddingHorizontal: dimensions.md,
            justifyContent: 'space-between',
          }}>
            {featuredProducts.map((product) => (
              <ProductGridItem key={product.id} product={product} />
            ))}
          </View>
        </View>

        {/* Live Shopping Events */}
        <View style={{ paddingVertical: dimensions.md, paddingBottom: dimensions.xl }}>
          <Text style={[commonStyles.heading3, { paddingHorizontal: dimensions.md, marginBottom: dimensions.md }]}>
            Live Shopping Events
          </Text>
          
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{ paddingHorizontal: dimensions.md }}
          >
            {[1, 2, 3].map((item) => (
              <TouchableOpacity
                key={item}
                style={{
                  backgroundColor: colors.darkGray,
                  borderRadius: dimensions.radiusMd,
                  padding: dimensions.md,
                  marginRight: dimensions.md,
                  width: 200,
                  height: 120,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                <Text style={{ fontSize: 32, marginBottom: dimensions.sm }}>🔴</Text>
                <Text style={[commonStyles.bodyMedium, { fontWeight: 'bold', textAlign: 'center' }]}>
                  Live Shopping Event #{item}
                </Text>
                <Text style={[commonStyles.bodySmall, { color: colors.red, marginTop: dimensions.xs }]}>
                  LIVE NOW
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default DiscoverScreen;
