import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  SafeAreaView,
  ScrollView,
  FlatList,
  Dimensions,
} from 'react-native';
import { colors, dimensions, commonStyles } from '../styles/tiktokStyles';
import { firestore } from '../services/firebase';

const { width: screenWidth } = Dimensions.get('window');

const TraderScreen = ({ traderId, onClose }) => {
  const [trader, setTrader] = useState(null);
  const [products, setProducts] = useState([]);
  const [isFollowing, setIsFollowing] = useState(false);
  const [activeTab, setActiveTab] = useState('products');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadTraderData();
  }, [traderId]);

  const loadTraderData = async () => {
    try {
      setLoading(true);
      
      // Load trader info
      const traderDoc = await firestore.collection('users').doc(traderId).get();
      if (traderDoc.exists) {
        setTrader({ id: traderDoc.id, ...traderDoc.data() });
      }

      // Load trader's products
      const productsSnapshot = await firestore
        .collection('products')
        .where('sellerId', '==', traderId)
        .get();
      
      const productsData = productsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      }));
      
      setProducts(productsData);
    } catch (error) {
      console.error('Error loading trader data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFollow = () => {
    setIsFollowing(!isFollowing);
    // Update follower count
    if (trader) {
      setTrader(prev => ({
        ...prev,
        followers: isFollowing ? prev.followers - 1 : prev.followers + 1,
      }));
    }
  };

  const formatNumber = (num) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const StatItem = ({ number, label }) => (
    <View style={{ alignItems: 'center', flex: 1 }}>
      <Text style={[commonStyles.heading2, { color: colors.white }]}>
        {formatNumber(number)}
      </Text>
      <Text style={[commonStyles.bodySmall, { color: colors.secondaryText }]}>
        {label}
      </Text>
    </View>
  );

  const TabButton = ({ title, isActive, onPress }) => (
    <TouchableOpacity
      style={{
        flex: 1,
        paddingVertical: dimensions.md,
        borderBottomWidth: 2,
        borderBottomColor: isActive ? colors.red : 'transparent',
        alignItems: 'center',
      }}
      onPress={onPress}
    >
      <Text style={{
        color: isActive ? colors.white : colors.secondaryText,
        fontWeight: isActive ? 'bold' : 'normal',
        fontSize: 16,
      }}>
        {title}
      </Text>
    </TouchableOpacity>
  );

  const ProductGridItem = ({ product }) => (
    <TouchableOpacity
      style={{
        width: (screenWidth - dimensions.md * 3) / 2,
        marginRight: dimensions.sm,
        marginBottom: dimensions.sm,
        backgroundColor: colors.darkGray,
        borderRadius: dimensions.radiusMd,
        overflow: 'hidden',
      }}
    >
      <Image
        source={{ uri: product.images?.[0] }}
        style={{
          width: '100%',
          height: 200,
        }}
        resizeMode="cover"
      />
      
      <View style={{ padding: dimensions.sm }}>
        <Text style={[commonStyles.bodyMedium, { fontWeight: 'bold', marginBottom: dimensions.xs }]} numberOfLines={2}>
          {product.name}
        </Text>
        
        <Text style={[commonStyles.bodyLarge, { color: colors.red, fontWeight: 'bold' }]}>
          ${product.price}
        </Text>
        
        <View style={[commonStyles.row, { marginTop: dimensions.xs }]}>
          <Text style={[commonStyles.bodySmall, { color: colors.secondaryText }]}>
            ❤️ {formatNumber(product.likes)}
          </Text>
          <Text style={[commonStyles.bodySmall, { color: colors.secondaryText, marginLeft: dimensions.sm }]}>
            💬 {formatNumber(product.comments)}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <SafeAreaView style={[commonStyles.container, commonStyles.center]}>
        <Text style={commonStyles.bodyLarge}>Loading trader profile...</Text>
      </SafeAreaView>
    );
  }

  if (!trader) {
    return (
      <SafeAreaView style={[commonStyles.container, commonStyles.center]}>
        <Text style={commonStyles.bodyLarge}>Trader not found</Text>
        <TouchableOpacity style={commonStyles.primaryButton} onPress={onClose}>
          <Text style={[commonStyles.bodyMedium, { color: colors.white }]}>Go Back</Text>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={commonStyles.container}>
      {/* Header */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: dimensions.md,
        paddingVertical: dimensions.md,
        borderBottomWidth: 1,
        borderBottomColor: colors.border,
      }}>
        <TouchableOpacity onPress={onClose}>
          <Text style={{ color: colors.white, fontSize: 24 }}>←</Text>
        </TouchableOpacity>
        
        <Text style={[commonStyles.heading3]}>
          @{trader.name}
        </Text>
        
        <TouchableOpacity>
          <Text style={{ color: colors.white, fontSize: 24 }}>⋯</Text>
        </TouchableOpacity>
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Profile Section */}
        <View style={{
          alignItems: 'center',
          paddingHorizontal: dimensions.md,
          paddingVertical: dimensions.lg,
        }}>
          {/* Avatar */}
          <View style={{ position: 'relative', marginBottom: dimensions.md }}>
            <Image
              source={{ uri: trader.avatar }}
              style={{
                width: 120,
                height: 120,
                borderRadius: 60,
                borderWidth: 3,
                borderColor: colors.red,
              }}
            />
            
            {trader.verified && (
              <View style={{
                position: 'absolute',
                bottom: 0,
                right: 0,
                backgroundColor: colors.blue,
                borderRadius: 15,
                width: 30,
                height: 30,
                alignItems: 'center',
                justifyContent: 'center',
                borderWidth: 3,
                borderColor: colors.black,
              }}>
                <Text style={{ color: colors.white, fontSize: 16 }}>✓</Text>
              </View>
            )}
          </View>

          {/* Trader Info */}
          <Text style={[commonStyles.heading2, { marginBottom: dimensions.xs }]}>
            {trader.name}
          </Text>
          
          <Text style={[commonStyles.bodyMedium, { color: colors.secondaryText, marginBottom: dimensions.md, textAlign: 'center' }]}>
            {trader.bio}
          </Text>

          {/* Stats */}
          <View style={{
            flexDirection: 'row',
            width: '100%',
            marginBottom: dimensions.lg,
          }}>
            <StatItem number={trader.followers} label="Followers" />
            <StatItem number={trader.following} label="Following" />
            <StatItem number={products.length} label="Products" />
          </View>

          {/* Action Buttons */}
          <View style={{
            flexDirection: 'row',
            width: '100%',
            marginBottom: dimensions.lg,
          }}>
            <TouchableOpacity
              style={[
                isFollowing ? commonStyles.secondaryButton : commonStyles.primaryButton,
                { flex: 1, marginRight: dimensions.sm }
              ]}
              onPress={handleFollow}
            >
              <Text style={[
                commonStyles.bodyMedium, 
                { 
                  color: colors.white, 
                  fontWeight: 'bold' 
                }
              ]}>
                {isFollowing ? 'Following' : 'Follow'}
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                commonStyles.secondaryButton,
                { flex: 1, marginLeft: dimensions.sm }
              ]}
            >
              <Text style={[commonStyles.bodyMedium, { color: colors.white, fontWeight: 'bold' }]}>
                Message
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Tabs */}
        <View style={{
          flexDirection: 'row',
          borderBottomWidth: 1,
          borderBottomColor: colors.border,
          marginHorizontal: dimensions.md,
        }}>
          <TabButton
            title={`Products (${products.length})`}
            isActive={activeTab === 'products'}
            onPress={() => setActiveTab('products')}
          />
          <TabButton
            title="Reviews"
            isActive={activeTab === 'reviews'}
            onPress={() => setActiveTab('reviews')}
          />
        </View>

        {/* Content */}
        <View style={{
          paddingHorizontal: dimensions.md,
          paddingTop: dimensions.md,
          paddingBottom: dimensions.xl,
        }}>
          {activeTab === 'products' ? (
            <View style={{
              flexDirection: 'row',
              flexWrap: 'wrap',
              justifyContent: 'space-between',
            }}>
              {products.map((product) => (
                <ProductGridItem key={product.id} product={product} />
              ))}
            </View>
          ) : (
            <View style={[commonStyles.center, { paddingVertical: dimensions.xl }]}>
              <Text style={{ fontSize: 48, marginBottom: dimensions.md }}>⭐</Text>
              <Text style={[commonStyles.heading3, { marginBottom: dimensions.sm }]}>
                4.8/5 Rating
              </Text>
              <Text style={[commonStyles.bodyMedium, { color: colors.secondaryText, textAlign: 'center' }]}>
                Based on 1,234 reviews
              </Text>
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default TraderScreen;
