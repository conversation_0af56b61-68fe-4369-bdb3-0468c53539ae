import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Animated, Dimensions } from 'react-native';
import { useAuth } from '../context/AuthContext';
import { useCart } from '../context/CartContext';
import HomeScreen from '../screens/HomeScreen';
import CartScreen from '../screens/CartScreen';
import ProfileScreen from '../screens/ProfileScreen';
import AuthScreen from '../screens/AuthScreen';
import DiscoverScreen from '../screens/DiscoverScreen';
import InboxScreen from '../screens/InboxScreen';
import { colors, dimensions } from '../styles/tiktokStyles';

const { width: screenWidth } = Dimensions.get('window');

// TikTok-style navigation with enhanced features
const MainNavigator = () => {
  const { isAuthenticated } = useAuth();
  const { getCartItemCount } = useCart();
  const [activeTab, setActiveTab] = useState('home');
  const [tabAnimation] = useState(new Animated.Value(0));

  if (!isAuthenticated) {
    return <AuthScreen />;
  }

  const renderScreen = () => {
    switch (activeTab) {
      case 'home':
        return <HomeScreen />;
      case 'discover':
        return <DiscoverScreen />;
      case 'inbox':
        return <InboxScreen />;
      case 'cart':
        return <CartScreen />;
      case 'profile':
        return <ProfileScreen />;
      default:
        return <HomeScreen />;
    }
  };

  const handleTabPress = (tab) => {
    setActiveTab(tab);

    // Animate tab transition
    Animated.spring(tabAnimation, {
      toValue: 1,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start(() => {
      tabAnimation.setValue(0);
    });
  };

  const TabButton = ({ tab, icon, label, isActive, onPress, isSpecial = false }) => {
    const scaleValue = isActive ? 1.05 : 1;

    return (
      <TouchableOpacity
        style={{
          flex: isSpecial ? 1.3 : 1,
          alignItems: 'center',
          justifyContent: 'center',
          paddingVertical: dimensions.xs,
          paddingHorizontal: dimensions.xs,
        }}
        onPress={onPress}
        activeOpacity={0.8}
      >
        <Animated.View style={{
          transform: [{ scale: scaleValue }],
          alignItems: 'center',
        }}>
          <View style={{
            width: isSpecial ? 52 : 32,
            height: isSpecial ? 52 : 32,
            alignItems: 'center',
            justifyContent: 'center',
            marginBottom: isSpecial ? 0 : 4,
            backgroundColor: isSpecial ? colors.red : (isActive ? 'rgba(254, 44, 85, 0.1)' : 'transparent'),
            borderRadius: isSpecial ? 12 : (isActive ? 16 : 0),
            borderWidth: isSpecial ? 2 : 0,
            borderColor: isSpecial ? colors.white : 'transparent',
            shadowColor: isSpecial ? colors.red : 'transparent',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: isSpecial ? 0.3 : 0,
            shadowRadius: isSpecial ? 4 : 0,
            elevation: isSpecial ? 6 : 0,
          }}>
            <Text style={{
              fontSize: isSpecial ? 28 : 24,
              color: isSpecial ? colors.white : (isActive ? colors.red : colors.secondaryText),
            }}>
              {icon}
            </Text>

            {/* Cart Badge */}
            {tab === 'cart' && getCartItemCount() > 0 && (
              <View style={{
                position: 'absolute',
                top: -6,
                right: -6,
                backgroundColor: colors.red,
                borderRadius: 12,
                minWidth: 24,
                height: 24,
                alignItems: 'center',
                justifyContent: 'center',
                borderWidth: 2,
                borderColor: colors.black,
              }}>
                <Text style={{
                  color: colors.white,
                  fontSize: 10,
                  fontWeight: 'bold',
                }}>
                  {getCartItemCount() > 99 ? '99+' : getCartItemCount()}
                </Text>
              </View>
            )}

            {/* Inbox Badge */}
            {tab === 'inbox' && (
              <View style={{
                position: 'absolute',
                top: -3,
                right: -3,
                backgroundColor: colors.red,
                borderRadius: 8,
                width: 16,
                height: 16,
                borderWidth: 2,
                borderColor: colors.black,
              }} />
            )}
          </View>

          {!isSpecial && (
            <Text style={{
              fontSize: 10,
              color: isActive ? colors.red : colors.secondaryText,
              fontWeight: isActive ? 'bold' : '500',
              marginTop: 2,
            }}>
              {label}
            </Text>
          )}
        </Animated.View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={{ flex: 1, backgroundColor: colors.black }}>
      {/* Main Content */}
      <View style={{ flex: 1 }}>
        {renderScreen()}
      </View>

      {/* Enhanced TikTok-Style Bottom Tab Bar */}
      <View style={{
        flexDirection: 'row',
        backgroundColor: colors.black,
        borderTopWidth: 0.5,
        borderTopColor: 'rgba(255, 255, 255, 0.1)',
        paddingBottom: dimensions.md,
        paddingTop: dimensions.sm,
        paddingHorizontal: dimensions.xs,
        height: 85,
        shadowColor: colors.black,
        shadowOffset: { width: 0, height: -4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 12,
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 100,
      }}>
        <TabButton
          tab="home"
          icon="🏠"
          label="Home"
          isActive={activeTab === 'home'}
          onPress={() => handleTabPress('home')}
        />
        <TabButton
          tab="discover"
          icon="🔍"
          label="Discover"
          isActive={activeTab === 'discover'}
          onPress={() => handleTabPress('discover')}
        />
        <TabButton
          tab="cart"
          icon="+"
          label=""
          isActive={activeTab === 'cart'}
          onPress={() => handleTabPress('cart')}
          isSpecial={true}
        />
        <TabButton
          tab="inbox"
          icon="💬"
          label="Inbox"
          isActive={activeTab === 'inbox'}
          onPress={() => handleTabPress('inbox')}
        />
        <TabButton
          tab="profile"
          icon="👤"
          label="Profile"
          isActive={activeTab === 'profile'}
          onPress={() => handleTabPress('profile')}
        />
      </View>
    </View>
  );
};

export default MainNavigator;
