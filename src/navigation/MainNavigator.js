import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Animated, Dimensions } from 'react-native';
import { useAuth } from '../context/AuthContext';
import { useCart } from '../context/CartContext';
import HomeScreen from '../screens/HomeScreen';
import CartScreen from '../screens/CartScreen';
import ProfileScreen from '../screens/ProfileScreen';
import AuthScreen from '../screens/AuthScreen';
import DiscoverScreen from '../screens/DiscoverScreen';
import InboxScreen from '../screens/InboxScreen';
import { colors, dimensions } from '../styles/tiktokStyles';

const { width: screenWidth } = Dimensions.get('window');

// TikTok-style navigation with enhanced features
const MainNavigator = () => {
  const { isAuthenticated } = useAuth();
  const { getCartItemCount } = useCart();
  const [activeTab, setActiveTab] = useState('home');
  const [tabAnimation] = useState(new Animated.Value(0));

  if (!isAuthenticated) {
    return <AuthScreen />;
  }

  const renderScreen = () => {
    switch (activeTab) {
      case 'home':
        return <HomeScreen />;
      case 'discover':
        return <DiscoverScreen />;
      case 'inbox':
        return <InboxScreen />;
      case 'cart':
        return <CartScreen />;
      case 'profile':
        return <ProfileScreen />;
      default:
        return <HomeScreen />;
    }
  };

  const handleTabPress = (tab) => {
    setActiveTab(tab);

    // Animate tab transition
    Animated.spring(tabAnimation, {
      toValue: 1,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start(() => {
      tabAnimation.setValue(0);
    });
  };

  const TabButton = ({ tab, icon, label, isActive, onPress, isSpecial = false }) => {
    const scaleValue = isActive ? 1.1 : 1;

    return (
      <TouchableOpacity
        style={{
          flex: isSpecial ? 1.2 : 1,
          alignItems: 'center',
          justifyContent: 'center',
          paddingVertical: dimensions.sm,
        }}
        onPress={onPress}
        activeOpacity={0.7}
      >
        <Animated.View style={{
          transform: [{ scale: scaleValue }],
          alignItems: 'center',
        }}>
          <View style={{
            width: isSpecial ? 48 : dimensions.iconLg,
            height: isSpecial ? 48 : dimensions.iconLg,
            alignItems: 'center',
            justifyContent: 'center',
            marginBottom: 4,
            backgroundColor: isSpecial ? colors.red : 'transparent',
            borderRadius: isSpecial ? 8 : 0,
            borderWidth: isSpecial ? 2 : 0,
            borderColor: isSpecial ? colors.white : 'transparent',
          }}>
            <Text style={{
              fontSize: isSpecial ? 24 : 20,
              color: isSpecial ? colors.white : (isActive ? colors.red : colors.secondaryText),
            }}>
              {icon}
            </Text>

            {/* Cart Badge */}
            {tab === 'cart' && getCartItemCount() > 0 && (
              <View style={{
                position: 'absolute',
                top: -4,
                right: -4,
                backgroundColor: colors.red,
                borderRadius: 10,
                minWidth: 20,
                height: 20,
                alignItems: 'center',
                justifyContent: 'center',
              }}>
                <Text style={{
                  color: colors.white,
                  fontSize: 10,
                  fontWeight: 'bold',
                }}>
                  {getCartItemCount() > 99 ? '99+' : getCartItemCount()}
                </Text>
              </View>
            )}

            {/* Inbox Badge */}
            {tab === 'inbox' && (
              <View style={{
                position: 'absolute',
                top: -2,
                right: -2,
                backgroundColor: colors.red,
                borderRadius: 6,
                width: 12,
                height: 12,
              }} />
            )}
          </View>

          {!isSpecial && (
            <Text style={{
              fontSize: 10,
              color: isActive ? colors.red : colors.secondaryText,
              fontWeight: isActive ? 'bold' : 'normal',
            }}>
              {label}
            </Text>
          )}
        </Animated.View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={{ flex: 1, backgroundColor: colors.black }}>
      {/* Main Content */}
      <View style={{ flex: 1 }}>
        {renderScreen()}
      </View>

      {/* TikTok-Style Bottom Tab Bar */}
      <View style={{
        flexDirection: 'row',
        backgroundColor: colors.black,
        borderTopWidth: 0.5,
        borderTopColor: colors.border,
        paddingBottom: dimensions.sm,
        paddingTop: dimensions.xs,
        height: dimensions.tabBarHeight,
        shadowColor: colors.black,
        shadowOffset: { width: 0, height: -2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 8,
      }}>
        <TabButton
          tab="home"
          icon="🏠"
          label="Home"
          isActive={activeTab === 'home'}
          onPress={() => handleTabPress('home')}
        />
        <TabButton
          tab="discover"
          icon="🔍"
          label="Discover"
          isActive={activeTab === 'discover'}
          onPress={() => handleTabPress('discover')}
        />
        <TabButton
          tab="cart"
          icon="+"
          label=""
          isActive={activeTab === 'cart'}
          onPress={() => handleTabPress('cart')}
          isSpecial={true}
        />
        <TabButton
          tab="inbox"
          icon="💬"
          label="Inbox"
          isActive={activeTab === 'inbox'}
          onPress={() => handleTabPress('inbox')}
        />
        <TabButton
          tab="profile"
          icon="👤"
          label="Profile"
          isActive={activeTab === 'profile'}
          onPress={() => handleTabPress('profile')}
        />
      </View>
    </View>
  );
};

export default MainNavigator;
