import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useAuth } from '../context/AuthContext';
import { useCart } from '../context/CartContext';
import HomeScreen from '../screens/HomeScreen';
import CartScreen from '../screens/CartScreen';
import ProfileScreen from '../screens/ProfileScreen';
import AuthScreen from '../screens/AuthScreen';
import { colors, dimensions } from '../styles/tiktokStyles';

// Simple tab navigation (we'll enhance this later with proper navigation)
const MainNavigator = () => {
  const { isAuthenticated } = useAuth();
  const { getCartItemCount } = useCart();
  const [activeTab, setActiveTab] = React.useState('home');

  if (!isAuthenticated) {
    return <AuthScreen />;
  }

  const renderScreen = () => {
    switch (activeTab) {
      case 'home':
        return <HomeScreen />;
      case 'cart':
        return <CartScreen />;
      case 'profile':
        return <ProfileScreen />;
      default:
        return <HomeScreen />;
    }
  };

  const TabButton = ({ tab, icon, label, isActive, onPress }) => (
    <TouchableOpacity
      style={{
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: dimensions.sm,
      }}
      onPress={onPress}
    >
      <View style={{
        width: dimensions.iconLg,
        height: dimensions.iconLg,
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 4,
      }}>
        <Text style={{
          fontSize: 20,
          color: isActive ? colors.red : colors.secondaryText,
        }}>
          {icon}
        </Text>
        {tab === 'cart' && getCartItemCount() > 0 && (
          <View style={{
            position: 'absolute',
            top: -4,
            right: -4,
            backgroundColor: colors.red,
            borderRadius: 10,
            minWidth: 20,
            height: 20,
            alignItems: 'center',
            justifyContent: 'center',
          }}>
            <Text style={{
              color: colors.white,
              fontSize: 10,
              fontWeight: 'bold',
            }}>
              {getCartItemCount() > 99 ? '99+' : getCartItemCount()}
            </Text>
          </View>
        )}
      </View>
      <Text style={{
        fontSize: 10,
        color: isActive ? colors.red : colors.secondaryText,
        fontWeight: isActive ? 'bold' : 'normal',
      }}>
        {label}
      </Text>
    </TouchableOpacity>
  );

  return (
    <View style={{ flex: 1, backgroundColor: colors.black }}>
      {/* Main Content */}
      <View style={{ flex: 1 }}>
        {renderScreen()}
      </View>

      {/* Bottom Tab Bar */}
      <View style={{
        flexDirection: 'row',
        backgroundColor: colors.darkGray,
        borderTopWidth: 1,
        borderTopColor: colors.border,
        paddingBottom: dimensions.sm,
        paddingTop: dimensions.xs,
        height: dimensions.tabBarHeight,
      }}>
        <TabButton
          tab="home"
          icon="🏠"
          label="Home"
          isActive={activeTab === 'home'}
          onPress={() => setActiveTab('home')}
        />
        <TabButton
          tab="cart"
          icon="🛒"
          label="Cart"
          isActive={activeTab === 'cart'}
          onPress={() => setActiveTab('cart')}
        />
        <TabButton
          tab="profile"
          icon="👤"
          label="Profile"
          isActive={activeTab === 'profile'}
          onPress={() => setActiveTab('profile')}
        />
      </View>
    </View>
  );
};

export default MainNavigator;
