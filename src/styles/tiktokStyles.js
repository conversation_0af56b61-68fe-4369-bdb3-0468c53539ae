import { StyleSheet, Dimensions } from 'react-native';

const { width, height } = Dimensions.get('window');

// TikTok Color Palette
export const colors = {
  // Primary TikTok colors
  black: '#000000',
  white: '#FFFFFF',
  red: '#FE2C55', // TikTok red
  blue: '#25F4EE', // TikTok blue/cyan
  
  // Background colors
  darkGray: '#161823',
  mediumGray: '#1F1F23',
  lightGray: '#2F2F35',
  
  // Text colors
  primaryText: '#FFFFFF',
  secondaryText: '#A8A8A8',
  mutedText: '#6A6A6A',
  
  // Accent colors
  pink: '#FF006E',
  purple: '#8B5CF6',
  green: '#00F5A0',
  yellow: '#FFFF00',
  
  // UI colors
  border: '#2F2F35',
  overlay: 'rgba(0, 0, 0, 0.6)',
  success: '#00D4AA',
  error: '#FF3B30',
  warning: '#FF9500',
};

// TikTok Typography
export const typography = {
  // Font families (TikTok uses custom fonts, we'll use system fonts)
  primary: 'System',
  bold: 'System',
  
  // Font sizes
  xs: 10,
  sm: 12,
  base: 14,
  lg: 16,
  xl: 18,
  '2xl': 20,
  '3xl': 24,
  '4xl': 28,
  '5xl': 32,
  '6xl': 36,
  
  // Font weights
  light: '300',
  normal: '400',
  medium: '500',
  semibold: '600',
  bold: '700',
  extrabold: '800',
};

// Common dimensions
export const dimensions = {
  screenWidth: width,
  screenHeight: height,
  
  // Spacing
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  '2xl': 48,
  '3xl': 64,
  
  // Border radius
  radiusXs: 4,
  radiusSm: 8,
  radiusMd: 12,
  radiusLg: 16,
  radiusXl: 24,
  radiusFull: 9999,
  
  // Common sizes
  iconSm: 16,
  iconMd: 24,
  iconLg: 32,
  iconXl: 48,
  
  // Button heights
  buttonSm: 32,
  buttonMd: 44,
  buttonLg: 56,
  
  // Header height
  headerHeight: 60,
  tabBarHeight: 80,
};

// TikTok-style common styles
export const commonStyles = StyleSheet.create({
  // Container styles
  container: {
    flex: 1,
    backgroundColor: colors.black,
  },
  
  safeArea: {
    flex: 1,
    backgroundColor: colors.black,
  },
  
  // Text styles
  heading1: {
    fontSize: typography['4xl'],
    fontWeight: typography.bold,
    color: colors.primaryText,
    lineHeight: 36,
  },
  
  heading2: {
    fontSize: typography['3xl'],
    fontWeight: typography.bold,
    color: colors.primaryText,
    lineHeight: 32,
  },
  
  heading3: {
    fontSize: typography['2xl'],
    fontWeight: typography.semibold,
    color: colors.primaryText,
    lineHeight: 28,
  },
  
  bodyLarge: {
    fontSize: typography.lg,
    fontWeight: typography.normal,
    color: colors.primaryText,
    lineHeight: 24,
  },
  
  bodyMedium: {
    fontSize: typography.base,
    fontWeight: typography.normal,
    color: colors.primaryText,
    lineHeight: 20,
  },
  
  bodySmall: {
    fontSize: typography.sm,
    fontWeight: typography.normal,
    color: colors.secondaryText,
    lineHeight: 16,
  },
  
  caption: {
    fontSize: typography.xs,
    fontWeight: typography.normal,
    color: colors.mutedText,
    lineHeight: 14,
  },
  
  // Button styles
  primaryButton: {
    backgroundColor: colors.red,
    paddingHorizontal: dimensions.lg,
    paddingVertical: dimensions.md,
    borderRadius: dimensions.radiusMd,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: dimensions.buttonMd,
  },
  
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: colors.white,
    paddingHorizontal: dimensions.lg,
    paddingVertical: dimensions.md,
    borderRadius: dimensions.radiusMd,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: dimensions.buttonMd,
  },
  
  ghostButton: {
    backgroundColor: 'transparent',
    paddingHorizontal: dimensions.lg,
    paddingVertical: dimensions.md,
    borderRadius: dimensions.radiusMd,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: dimensions.buttonMd,
  },
  
  // Input styles
  input: {
    backgroundColor: colors.darkGray,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: dimensions.radiusMd,
    paddingHorizontal: dimensions.md,
    paddingVertical: dimensions.md,
    fontSize: typography.base,
    color: colors.primaryText,
    minHeight: dimensions.buttonMd,
  },
  
  // Card styles
  card: {
    backgroundColor: colors.darkGray,
    borderRadius: dimensions.radiusMd,
    padding: dimensions.md,
    marginVertical: dimensions.sm,
  },
  
  // Shadow styles (for iOS)
  shadow: {
    shadowColor: colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5, // for Android
  },
  
  // Layout helpers
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  
  column: {
    flexDirection: 'column',
  },
  
  center: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  spaceBetween: {
    justifyContent: 'space-between',
  },
  
  spaceAround: {
    justifyContent: 'space-around',
  },
  
  // Margin and padding helpers
  mt1: { marginTop: dimensions.xs },
  mt2: { marginTop: dimensions.sm },
  mt3: { marginTop: dimensions.md },
  mt4: { marginTop: dimensions.lg },
  
  mb1: { marginBottom: dimensions.xs },
  mb2: { marginBottom: dimensions.sm },
  mb3: { marginBottom: dimensions.md },
  mb4: { marginBottom: dimensions.lg },
  
  ml1: { marginLeft: dimensions.xs },
  ml2: { marginLeft: dimensions.sm },
  ml3: { marginLeft: dimensions.md },
  ml4: { marginLeft: dimensions.lg },
  
  mr1: { marginRight: dimensions.xs },
  mr2: { marginRight: dimensions.sm },
  mr3: { marginRight: dimensions.md },
  mr4: { marginRight: dimensions.lg },
  
  pt1: { paddingTop: dimensions.xs },
  pt2: { paddingTop: dimensions.sm },
  pt3: { paddingTop: dimensions.md },
  pt4: { paddingTop: dimensions.lg },
  
  pb1: { paddingBottom: dimensions.xs },
  pb2: { paddingBottom: dimensions.sm },
  pb3: { paddingBottom: dimensions.md },
  pb4: { paddingBottom: dimensions.lg },
  
  pl1: { paddingLeft: dimensions.xs },
  pl2: { paddingLeft: dimensions.sm },
  pl3: { paddingLeft: dimensions.md },
  pl4: { paddingLeft: dimensions.lg },
  
  pr1: { paddingRight: dimensions.xs },
  pr2: { paddingRight: dimensions.sm },
  pr3: { paddingRight: dimensions.md },
  pr4: { paddingRight: dimensions.lg },
});

export default {
  colors,
  typography,
  dimensions,
  commonStyles,
};
