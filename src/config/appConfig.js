// StoreTok App Configuration
export const APP_CONFIG = {
  // App Information
  APP_NAME: 'StoreTok',
  APP_VERSION: '1.0.0',
  APP_DESCRIPTION: 'TikTok-style ecommerce shopping experience',
  
  // API Configuration
  API_BASE_URL: 'https://api.storetok.com',
  API_VERSION: 'v1',
  API_TIMEOUT: 10000, // 10 seconds
  
  // Firebase Configuration (Mock - replace with real config)
  FIREBASE_CONFIG: {
    apiKey: "your-api-key",
    authDomain: "store-tok.firebaseapp.com",
    projectId: "store-tok",
    storageBucket: "store-tok.appspot.com",
    messagingSenderId: "123456789",
    appId: "your-app-id"
  },
  
  // Feature Flags
  FEATURES: {
    LIVE_SHOPPING: true,
    VIDEO_PRODUCTS: true,
    SOCIAL_LOGIN: true,
    PUSH_NOTIFICATIONS: true,
    ANALYTICS: true,
    CRASH_REPORTING: true,
    DEEP_LINKING: true,
    OFFLINE_MODE: false,
  },
  
  // UI Configuration
  UI_CONFIG: {
    // Animation durations (ms)
    ANIMATION_DURATION_SHORT: 200,
    ANIMATION_DURATION_MEDIUM: 300,
    ANIMATION_DURATION_LONG: 500,
    
    // Pagination
    PRODUCTS_PER_PAGE: 10,
    COMMENTS_PER_PAGE: 20,
    MESSAGES_PER_PAGE: 50,
    
    // Media
    MAX_IMAGE_SIZE: 5 * 1024 * 1024, // 5MB
    MAX_VIDEO_SIZE: 50 * 1024 * 1024, // 50MB
    MAX_VIDEO_DURATION: 60, // seconds
    
    // Text limits
    MAX_COMMENT_LENGTH: 500,
    MAX_REVIEW_LENGTH: 1000,
    MAX_PRODUCT_NAME_LENGTH: 100,
    MAX_PRODUCT_DESCRIPTION_LENGTH: 2000,
  },
  
  // Social Media Links
  SOCIAL_LINKS: {
    WEBSITE: 'https://storetok.com',
    SUPPORT_EMAIL: '<EMAIL>',
    TWITTER: 'https://twitter.com/storetok',
    INSTAGRAM: 'https://instagram.com/storetok',
    FACEBOOK: 'https://facebook.com/storetok',
    TIKTOK: 'https://tiktok.com/@storetok',
  },
  
  // Payment Configuration
  PAYMENT_CONFIG: {
    SUPPORTED_CURRENCIES: ['USD', 'EUR', 'GBP', 'CAD', 'AUD'],
    DEFAULT_CURRENCY: 'USD',
    PAYMENT_METHODS: ['card', 'paypal', 'apple_pay', 'google_pay'],
    MIN_ORDER_AMOUNT: 1.00,
    MAX_ORDER_AMOUNT: 10000.00,
    SHIPPING_THRESHOLD_FREE: 50.00, // Free shipping above this amount
  },
  
  // Notification Configuration
  NOTIFICATIONS: {
    TYPES: {
      ORDER_CONFIRMED: 'order_confirmed',
      ORDER_SHIPPED: 'order_shipped',
      ORDER_DELIVERED: 'order_delivered',
      NEW_FOLLOWER: 'new_follower',
      NEW_LIKE: 'new_like',
      NEW_COMMENT: 'new_comment',
      PRODUCT_BACK_IN_STOCK: 'product_back_in_stock',
      PRICE_DROP: 'price_drop',
      LIVE_SHOPPING_STARTED: 'live_shopping_started',
    },
    DEFAULT_SETTINGS: {
      order_updates: true,
      social_interactions: true,
      marketing: false,
      live_events: true,
      price_alerts: true,
    }
  },
  
  // Cache Configuration
  CACHE_CONFIG: {
    PRODUCTS_CACHE_DURATION: 5 * 60 * 1000, // 5 minutes
    USER_CACHE_DURATION: 10 * 60 * 1000, // 10 minutes
    IMAGES_CACHE_DURATION: 24 * 60 * 60 * 1000, // 24 hours
    MAX_CACHE_SIZE: 100 * 1024 * 1024, // 100MB
  },
  
  // Analytics Events
  ANALYTICS_EVENTS: {
    // User Actions
    USER_SIGNUP: 'user_signup',
    USER_LOGIN: 'user_login',
    USER_LOGOUT: 'user_logout',
    
    // Product Actions
    PRODUCT_VIEW: 'product_view',
    PRODUCT_LIKE: 'product_like',
    PRODUCT_SHARE: 'product_share',
    PRODUCT_COMMENT: 'product_comment',
    
    // Shopping Actions
    ADD_TO_CART: 'add_to_cart',
    REMOVE_FROM_CART: 'remove_from_cart',
    CHECKOUT_START: 'checkout_start',
    CHECKOUT_COMPLETE: 'checkout_complete',
    
    // Social Actions
    FOLLOW_USER: 'follow_user',
    UNFOLLOW_USER: 'unfollow_user',
    SHARE_PROFILE: 'share_profile',
    
    // Live Shopping
    JOIN_LIVE_EVENT: 'join_live_event',
    LEAVE_LIVE_EVENT: 'leave_live_event',
    LIVE_PURCHASE: 'live_purchase',
  },
  
  // Error Messages
  ERROR_MESSAGES: {
    NETWORK_ERROR: 'Network connection error. Please check your internet connection.',
    SERVER_ERROR: 'Server error. Please try again later.',
    AUTHENTICATION_ERROR: 'Authentication failed. Please log in again.',
    PERMISSION_DENIED: 'Permission denied. Please check your account permissions.',
    PRODUCT_NOT_FOUND: 'Product not found or no longer available.',
    CART_EMPTY: 'Your cart is empty. Add some items to continue.',
    PAYMENT_FAILED: 'Payment failed. Please try a different payment method.',
    UPLOAD_FAILED: 'File upload failed. Please try again.',
    INVALID_INPUT: 'Invalid input. Please check your information.',
    RATE_LIMIT_EXCEEDED: 'Too many requests. Please wait a moment and try again.',
  },
  
  // Success Messages
  SUCCESS_MESSAGES: {
    PRODUCT_ADDED_TO_CART: 'Product added to cart successfully!',
    ORDER_PLACED: 'Order placed successfully!',
    REVIEW_SUBMITTED: 'Review submitted successfully!',
    PROFILE_UPDATED: 'Profile updated successfully!',
    PASSWORD_CHANGED: 'Password changed successfully!',
    EMAIL_VERIFIED: 'Email verified successfully!',
    LOGOUT_SUCCESS: 'Logged out successfully!',
    FOLLOW_SUCCESS: 'Successfully followed user!',
    UNFOLLOW_SUCCESS: 'Successfully unfollowed user!',
  },
  
  // Development Configuration
  DEV_CONFIG: {
    ENABLE_LOGGING: __DEV__,
    ENABLE_DEBUG_MENU: __DEV__,
    MOCK_API_RESPONSES: true, // Set to false for production
    SHOW_PERFORMANCE_MONITOR: __DEV__,
    ENABLE_FLIPPER: __DEV__,
  },
  
  // App Store Configuration
  APP_STORE_CONFIG: {
    IOS_APP_ID: '123456789',
    ANDROID_PACKAGE_NAME: 'com.storetok.app',
    REVIEW_PROMPT_THRESHOLD: 5, // Show review prompt after 5 successful orders
    UPDATE_PROMPT_THRESHOLD: 7, // Show update prompt after 7 days
  },
};

// Environment-specific configurations
export const getEnvironmentConfig = () => {
  if (__DEV__) {
    return {
      ...APP_CONFIG,
      API_BASE_URL: 'https://dev-api.storetok.com',
      FEATURES: {
        ...APP_CONFIG.FEATURES,
        ANALYTICS: false,
        CRASH_REPORTING: false,
      }
    };
  }
  
  return APP_CONFIG;
};

export default APP_CONFIG;
