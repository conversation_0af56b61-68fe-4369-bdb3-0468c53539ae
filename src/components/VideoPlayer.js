import React, { useState, useRef } from 'react';
import {
  View,
  TouchableOpacity,
  Dimensions,
  Text,
} from 'react-native';
import { colors, dimensions } from '../styles/tiktokStyles';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const VideoPlayer = ({ 
  videoUri, 
  isActive = false, 
  style = {},
  onPress,
  showControls = true 
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(true);
  const [showPlayButton, setShowPlayButton] = useState(true);
  const videoRef = useRef(null);

  // Mock video player since we don't have expo-av installed yet
  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
    setShowPlayButton(false);
    
    // Hide play button after 2 seconds
    setTimeout(() => {
      setShowPlayButton(true);
    }, 2000);
  };

  const handleMuteToggle = () => {
    setIsMuted(!isMuted);
  };

  return (
    <View style={[
      {
        width: screenWidth,
        height: screenHeight,
        backgroundColor: colors.black,
        justifyContent: 'center',
        alignItems: 'center',
      },
      style
    ]}>
      {/* Mock Video Display */}
      <View style={{
        width: '100%',
        height: '100%',
        backgroundColor: colors.darkGray,
        justifyContent: 'center',
        alignItems: 'center',
      }}>
        <Text style={{
          fontSize: 64,
          marginBottom: dimensions.md,
        }}>
          🎥
        </Text>
        <Text style={{
          color: colors.white,
          fontSize: 18,
          fontWeight: 'bold',
          textAlign: 'center',
        }}>
          Product Video
        </Text>
        <Text style={{
          color: colors.secondaryText,
          fontSize: 14,
          textAlign: 'center',
          marginTop: dimensions.sm,
        }}>
          {isPlaying ? 'Playing...' : 'Tap to play'}
        </Text>
      </View>

      {/* Video Controls Overlay */}
      <TouchableOpacity
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          justifyContent: 'center',
          alignItems: 'center',
        }}
        onPress={onPress || handlePlayPause}
        activeOpacity={0.8}
      >
        {/* Play/Pause Button */}
        {showControls && showPlayButton && (
          <View style={{
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            borderRadius: 40,
            width: 80,
            height: 80,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
            <Text style={{
              color: colors.white,
              fontSize: 32,
            }}>
              {isPlaying ? '⏸️' : '▶️'}
            </Text>
          </View>
        )}
      </TouchableOpacity>

      {/* Mute Button */}
      {showControls && (
        <TouchableOpacity
          style={{
            position: 'absolute',
            top: dimensions.xl,
            right: dimensions.md,
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            borderRadius: 20,
            width: 40,
            height: 40,
            justifyContent: 'center',
            alignItems: 'center',
          }}
          onPress={handleMuteToggle}
        >
          <Text style={{
            color: colors.white,
            fontSize: 16,
          }}>
            {isMuted ? '🔇' : '🔊'}
          </Text>
        </TouchableOpacity>
      )}

      {/* Video Progress Bar */}
      {showControls && isPlaying && (
        <View style={{
          position: 'absolute',
          bottom: dimensions.xl * 3,
          left: dimensions.md,
          right: dimensions.md,
        }}>
          <View style={{
            height: 3,
            backgroundColor: 'rgba(255, 255, 255, 0.3)',
            borderRadius: 1.5,
          }}>
            <View style={{
              height: 3,
              backgroundColor: colors.red,
              borderRadius: 1.5,
              width: '30%', // Mock progress
            }} />
          </View>
        </View>
      )}

      {/* Video Info Overlay */}
      {showControls && (
        <View style={{
          position: 'absolute',
          bottom: dimensions.md,
          left: dimensions.md,
          right: dimensions.md,
        }}>
          <Text style={{
            color: colors.white,
            fontSize: 12,
            textAlign: 'center',
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            paddingHorizontal: dimensions.sm,
            paddingVertical: dimensions.xs,
            borderRadius: dimensions.radiusSm,
          }}>
            HD • {isPlaying ? 'Playing' : 'Paused'} • {isMuted ? 'Muted' : 'Sound On'}
          </Text>
        </View>
      )}
    </View>
  );
};

export default VideoPlayer;
