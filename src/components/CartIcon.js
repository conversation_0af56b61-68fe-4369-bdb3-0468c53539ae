import React, { useRef, useEffect } from 'react';
import {
  TouchableOpacity,
  Text,
  View,
  Animated,
} from 'react-native';
import { colors, dimensions } from '../styles/tiktokStyles';
import { useCart } from '../context/CartContext';

const CartIcon = ({ 
  onPress, 
  size = 48,
  iconSize = 20,
  showBadge = true,
  style = {} 
}) => {
  const { getCartItemCount } = useCart();
  const bounceAnimation = useRef(new Animated.Value(1)).current;
  const badgeAnimation = useRef(new Animated.Value(1)).current;
  const cartCount = getCartItemCount();

  useEffect(() => {
    if (cartCount > 0) {
      // Bounce animation when items are added
      Animated.sequence([
        Animated.timing(bounceAnimation, {
          toValue: 1.2,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(bounceAnimation, {
          toValue: 1,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start();

      // Badge pulse animation
      Animated.sequence([
        Animated.timing(badgeAnimation, {
          toValue: 1.3,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(badgeAnimation, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [cartCount]);

  return (
    <TouchableOpacity
      style={[
        {
          alignItems: 'center',
          justifyContent: 'center',
          position: 'relative',
        },
        style
      ]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <Animated.View 
        style={{
          width: size,
          height: size,
          borderRadius: size / 2,
          backgroundColor: cartCount > 0 ? colors.green : 'rgba(255, 255, 255, 0.2)',
          alignItems: 'center',
          justifyContent: 'center',
          transform: [{ scale: bounceAnimation }],
        }}
      >
        <Text style={{
          fontSize: iconSize,
          color: colors.white,
        }}>
          {cartCount > 0 ? '🛒' : '🛒'}
        </Text>
      </Animated.View>

      {/* Badge */}
      {showBadge && cartCount > 0 && (
        <Animated.View
          style={{
            position: 'absolute',
            top: -4,
            right: -4,
            backgroundColor: colors.red,
            borderRadius: 12,
            minWidth: 24,
            height: 24,
            alignItems: 'center',
            justifyContent: 'center',
            paddingHorizontal: 6,
            borderWidth: 2,
            borderColor: colors.black,
            transform: [{ scale: badgeAnimation }],
          }}
        >
          <Text style={{
            color: colors.white,
            fontSize: 10,
            fontWeight: 'bold',
          }}>
            {cartCount > 99 ? '99+' : cartCount}
          </Text>
        </Animated.View>
      )}

      {/* Success checkmark when items in cart */}
      {cartCount > 0 && (
        <View style={{
          position: 'absolute',
          bottom: -2,
          right: -2,
          backgroundColor: colors.green,
          borderRadius: 8,
          width: 16,
          height: 16,
          alignItems: 'center',
          justifyContent: 'center',
          borderWidth: 1,
          borderColor: colors.black,
        }}>
          <Text style={{
            color: colors.white,
            fontSize: 8,
            fontWeight: 'bold',
          }}>
            ✓
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );
};

export default CartIcon;
