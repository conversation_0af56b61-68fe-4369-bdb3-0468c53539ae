import React, { useRef, useEffect } from 'react';
import {
  TouchableOpacity,
  Text,
  View,
  Animated,
} from 'react-native';
import { colors, dimensions } from '../styles/tiktokStyles';

const LikeButton = ({ 
  isLiked, 
  likeCount, 
  onPress, 
  size = 48,
  iconSize = 20 
}) => {
  const scaleAnimation = useRef(new Animated.Value(1)).current;
  const pulseAnimation = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    if (isLiked) {
      // Heart pulse animation when liked
      Animated.sequence([
        Animated.timing(scaleAnimation, {
          toValue: 1.3,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnimation, {
          toValue: 1,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start();

      // Continuous pulse for liked state
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnimation, {
            toValue: 1.1,
            duration: 800,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnimation, {
            toValue: 1,
            duration: 800,
            useNativeDriver: true,
          }),
        ])
      ).start();
    } else {
      pulseAnimation.setValue(1);
    }
  }, [isLiked]);

  const formatNumber = (num) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  return (
    <TouchableOpacity
      style={{
        alignItems: 'center',
        marginVertical: dimensions.md,
      }}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <Animated.View 
        style={{
          width: size,
          height: size,
          borderRadius: size / 2,
          backgroundColor: isLiked ? colors.red : 'rgba(255, 255, 255, 0.2)',
          alignItems: 'center',
          justifyContent: 'center',
          marginBottom: dimensions.xs,
          transform: [
            { scale: scaleAnimation },
            { scale: isLiked ? pulseAnimation : 1 }
          ],
        }}
      >
        <Text style={{
          fontSize: iconSize,
          color: isLiked ? colors.white : colors.white,
        }}>
          {isLiked ? '❤️' : '🤍'}
        </Text>
      </Animated.View>
      
      <Text style={{
        fontSize: 12,
        color: colors.white,
        fontWeight: '600',
        textAlign: 'center',
      }}>
        {formatNumber(likeCount)}
      </Text>
    </TouchableOpacity>
  );
};

export default LikeButton;
