import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  ScrollView,
  Animated,
  Dimensions,
  Modal,
} from 'react-native';
import { colors, dimensions, commonStyles } from '../styles/tiktokStyles';

const { width: screenWidth } = Dimensions.get('window');

const LiveShopping = ({ 
  isVisible, 
  onClose, 
  liveEvent 
}) => {
  const [viewers, setViewers] = useState(1234);
  const [messages, setMessages] = useState([]);
  const [products, setProducts] = useState([]);
  const pulseAnimation = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    if (isVisible) {
      // Start live indicator animation
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnimation, {
            toValue: 1.2,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnimation, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      ).start();

      // Mock live data
      loadLiveData();
      
      // Simulate live updates
      const interval = setInterval(() => {
        updateLiveData();
      }, 3000);

      return () => clearInterval(interval);
    }
  }, [isVisible]);

  const loadLiveData = () => {
    setMessages([
      { id: 1, user: 'Sarah_123', message: 'Love this product! 😍', timestamp: Date.now() },
      { id: 2, user: 'Mike_Store', message: 'Special discount for live viewers!', timestamp: Date.now() - 1000 },
      { id: 3, user: 'Emma_W', message: 'How much is shipping?', timestamp: Date.now() - 2000 },
    ]);

    setProducts([
      {
        id: 1,
        name: 'Wireless Earbuds Pro',
        price: 79.99,
        originalPrice: 99.99,
        image: 'https://images.unsplash.com/photo-1572569511254-d8f925fe2cbb?w=400',
        stock: 15,
        sold: 85,
      },
      {
        id: 2,
        name: 'Smart Watch Series X',
        price: 199.99,
        originalPrice: 249.99,
        image: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400',
        stock: 8,
        sold: 42,
      },
    ]);
  };

  const updateLiveData = () => {
    // Update viewer count
    setViewers(prev => prev + Math.floor(Math.random() * 10) - 5);
    
    // Add new message
    const newMessages = [
      'Amazing quality!',
      'Just ordered mine!',
      'When will this be back in stock?',
      'Great price! 🔥',
      'Shipping to Europe?',
      'Love the color options',
    ];
    
    const randomMessage = newMessages[Math.floor(Math.random() * newMessages.length)];
    const randomUser = `User_${Math.floor(Math.random() * 1000)}`;
    
    setMessages(prev => [
      { id: Date.now(), user: randomUser, message: randomMessage, timestamp: Date.now() },
      ...prev.slice(0, 9) // Keep only last 10 messages
    ]);
  };

  const LiveMessage = ({ message }) => (
    <View style={{
      backgroundColor: 'rgba(0, 0, 0, 0.6)',
      borderRadius: dimensions.radiusSm,
      paddingHorizontal: dimensions.sm,
      paddingVertical: dimensions.xs,
      marginVertical: 2,
      maxWidth: '80%',
    }}>
      <Text style={{
        color: colors.blue,
        fontSize: 12,
        fontWeight: 'bold',
      }}>
        {message.user}
      </Text>
      <Text style={{
        color: colors.white,
        fontSize: 12,
      }}>
        {message.message}
      </Text>
    </View>
  );

  const ProductCard = ({ product }) => (
    <View style={{
      backgroundColor: colors.darkGray,
      borderRadius: dimensions.radiusMd,
      padding: dimensions.sm,
      marginRight: dimensions.sm,
      width: 150,
    }}>
      <Image
        source={{ uri: product.image }}
        style={{
          width: '100%',
          height: 80,
          borderRadius: dimensions.radiusSm,
          marginBottom: dimensions.sm,
        }}
        resizeMode="cover"
      />
      
      <Text style={[commonStyles.bodySmall, { fontWeight: 'bold', marginBottom: dimensions.xs }]} numberOfLines={2}>
        {product.name}
      </Text>
      
      <View style={[commonStyles.row, { alignItems: 'center', marginBottom: dimensions.xs }]}>
        <Text style={{
          color: colors.red,
          fontSize: 14,
          fontWeight: 'bold',
          marginRight: dimensions.xs,
        }}>
          ${product.price}
        </Text>
        <Text style={{
          color: colors.mutedText,
          fontSize: 10,
          textDecorationLine: 'line-through',
        }}>
          ${product.originalPrice}
        </Text>
      </View>

      <View style={{
        backgroundColor: colors.red,
        borderRadius: dimensions.radiusSm,
        paddingHorizontal: dimensions.xs,
        paddingVertical: 2,
        marginBottom: dimensions.sm,
      }}>
        <Text style={{
          color: colors.white,
          fontSize: 10,
          fontWeight: 'bold',
          textAlign: 'center',
        }}>
          {Math.round((product.originalPrice - product.price) / product.originalPrice * 100)}% OFF
        </Text>
      </View>

      <View style={[commonStyles.row, { justifyContent: 'space-between', marginBottom: dimensions.sm }]}>
        <Text style={[commonStyles.bodySmall, { color: colors.secondaryText }]}>
          Stock: {product.stock}
        </Text>
        <Text style={[commonStyles.bodySmall, { color: colors.green }]}>
          Sold: {product.sold}
        </Text>
      </View>

      <TouchableOpacity
        style={[
          commonStyles.primaryButton,
          {
            paddingVertical: dimensions.xs,
            minHeight: 32,
          }
        ]}
      >
        <Text style={{
          color: colors.white,
          fontSize: 12,
          fontWeight: 'bold',
        }}>
          Buy Now
        </Text>
      </TouchableOpacity>
    </View>
  );

  if (!isVisible) return null;

  return (
    <Modal
      visible={isVisible}
      animationType="slide"
      transparent={false}
      onRequestClose={onClose}
    >
      <View style={commonStyles.container}>
        {/* Live Video Background */}
        <View style={{
          flex: 1,
          backgroundColor: colors.darkGray,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
          <Text style={{ fontSize: 64, marginBottom: dimensions.md }}>📹</Text>
          <Text style={[commonStyles.heading2, { textAlign: 'center' }]}>
            Live Shopping Event
          </Text>
          <Text style={[commonStyles.bodyMedium, { color: colors.secondaryText, textAlign: 'center' }]}>
            Interactive shopping experience
          </Text>
        </View>

        {/* Live Indicator */}
        <View style={{
          position: 'absolute',
          top: 60,
          left: dimensions.md,
          flexDirection: 'row',
          alignItems: 'center',
        }}>
          <Animated.View style={{
            backgroundColor: colors.red,
            borderRadius: 8,
            paddingHorizontal: dimensions.sm,
            paddingVertical: dimensions.xs,
            flexDirection: 'row',
            alignItems: 'center',
            transform: [{ scale: pulseAnimation }],
          }}>
            <View style={{
              width: 8,
              height: 8,
              borderRadius: 4,
              backgroundColor: colors.white,
              marginRight: dimensions.xs,
            }} />
            <Text style={{
              color: colors.white,
              fontSize: 12,
              fontWeight: 'bold',
            }}>
              LIVE
            </Text>
          </Animated.View>
          
          <View style={{
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            borderRadius: dimensions.radiusSm,
            paddingHorizontal: dimensions.sm,
            paddingVertical: dimensions.xs,
            marginLeft: dimensions.sm,
          }}>
            <Text style={{
              color: colors.white,
              fontSize: 12,
            }}>
              👁️ {viewers.toLocaleString()}
            </Text>
          </View>
        </View>

        {/* Close Button */}
        <TouchableOpacity
          style={{
            position: 'absolute',
            top: 60,
            right: dimensions.md,
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            borderRadius: 20,
            width: 40,
            height: 40,
            alignItems: 'center',
            justifyContent: 'center',
          }}
          onPress={onClose}
        >
          <Text style={{ color: colors.white, fontSize: 18 }}>✕</Text>
        </TouchableOpacity>

        {/* Live Chat */}
        <View style={{
          position: 'absolute',
          left: dimensions.md,
          bottom: 200,
          width: screenWidth * 0.6,
          maxHeight: 200,
        }}>
          <ScrollView
            showsVerticalScrollIndicator={false}
            style={{ flex: 1 }}
          >
            {messages.map((message) => (
              <LiveMessage key={message.id} message={message} />
            ))}
          </ScrollView>
        </View>

        {/* Products Showcase */}
        <View style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          paddingVertical: dimensions.md,
        }}>
          <Text style={[commonStyles.heading3, { paddingHorizontal: dimensions.md, marginBottom: dimensions.md }]}>
            Featured Products
          </Text>
          
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{ paddingHorizontal: dimensions.md }}
          >
            {products.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </ScrollView>
        </View>

        {/* Action Buttons */}
        <View style={{
          position: 'absolute',
          right: dimensions.md,
          bottom: 120,
          alignItems: 'center',
        }}>
          <TouchableOpacity style={{
            backgroundColor: 'rgba(255, 255, 255, 0.2)',
            borderRadius: 25,
            width: 50,
            height: 50,
            alignItems: 'center',
            justifyContent: 'center',
            marginBottom: dimensions.md,
          }}>
            <Text style={{ fontSize: 24 }}>❤️</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={{
            backgroundColor: 'rgba(255, 255, 255, 0.2)',
            borderRadius: 25,
            width: 50,
            height: 50,
            alignItems: 'center',
            justifyContent: 'center',
            marginBottom: dimensions.md,
          }}>
            <Text style={{ fontSize: 24 }}>💬</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={{
            backgroundColor: 'rgba(255, 255, 255, 0.2)',
            borderRadius: 25,
            width: 50,
            height: 50,
            alignItems: 'center',
            justifyContent: 'center',
          }}>
            <Text style={{ fontSize: 24 }}>📤</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

export default LiveShopping;
