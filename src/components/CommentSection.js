import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  Image,
  KeyboardAvoidingView,
  Platform,
  Dimensions,
} from 'react-native';
import { colors, dimensions, commonStyles } from '../styles/tiktokStyles';
import { useAuth } from '../context/AuthContext';

const { height: screenHeight } = Dimensions.get('window');

const CommentSection = ({ 
  productId, 
  isVisible, 
  onClose,
  initialComments = [] 
}) => {
  const { user } = useAuth();
  const [comments, setComments] = useState(initialComments);
  const [newComment, setNewComment] = useState('');
  const [loading, setLoading] = useState(false);
  const flatListRef = useRef(null);

  const mockComments = [
    {
      id: '1',
      user: {
        id: 'user1',
        name: '<PERSON>',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
        verified: true,
      },
      text: 'Love this! Just ordered mine 😍',
      timestamp: '2h',
      likes: 24,
      isLiked: false,
      replies: [],
    },
    {
      id: '2',
      user: {
        id: 'user2',
        name: 'Mike Chen',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
        verified: false,
      },
      text: 'How is the quality? Thinking about getting one',
      timestamp: '4h',
      likes: 8,
      isLiked: false,
      replies: [
        {
          id: 'reply1',
          user: {
            id: 'user3',
            name: 'Emma Wilson',
            avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
          },
          text: 'Amazing quality! Totally worth it',
          timestamp: '3h',
          likes: 5,
        }
      ],
    },
    {
      id: '3',
      user: {
        id: 'user4',
        name: 'Alex Rodriguez',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
        verified: true,
      },
      text: 'Been using this for weeks, highly recommend! 🔥',
      timestamp: '1d',
      likes: 156,
      isLiked: true,
      replies: [],
    },
  ];

  React.useEffect(() => {
    if (comments.length === 0) {
      setComments(mockComments);
    }
  }, []);

  const handleSendComment = async () => {
    if (!newComment.trim()) return;

    setLoading(true);
    
    const comment = {
      id: Date.now().toString(),
      user: {
        id: user?.uid || 'current-user',
        name: user?.displayName || user?.email?.split('@')[0] || 'You',
        avatar: user?.photoURL || 'https://via.placeholder.com/150',
        verified: false,
      },
      text: newComment.trim(),
      timestamp: 'now',
      likes: 0,
      isLiked: false,
      replies: [],
    };

    setComments(prev => [comment, ...prev]);
    setNewComment('');
    setLoading(false);

    // Scroll to top to show new comment
    setTimeout(() => {
      flatListRef.current?.scrollToOffset({ offset: 0, animated: true });
    }, 100);
  };

  const handleLikeComment = (commentId) => {
    setComments(prev =>
      prev.map(comment =>
        comment.id === commentId
          ? {
              ...comment,
              isLiked: !comment.isLiked,
              likes: comment.isLiked ? comment.likes - 1 : comment.likes + 1,
            }
          : comment
      )
    );
  };

  const CommentItem = ({ comment, isReply = false }) => (
    <View style={{
      flexDirection: 'row',
      paddingHorizontal: dimensions.md,
      paddingVertical: dimensions.sm,
      marginLeft: isReply ? dimensions.xl : 0,
    }}>
      {/* Avatar */}
      <Image
        source={{ uri: comment.user.avatar }}
        style={{
          width: isReply ? 32 : 40,
          height: isReply ? 32 : 40,
          borderRadius: isReply ? 16 : 20,
          marginRight: dimensions.sm,
        }}
      />

      {/* Comment Content */}
      <View style={{ flex: 1 }}>
        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: dimensions.xs }}>
          <Text style={[commonStyles.bodyMedium, { fontWeight: 'bold', marginRight: dimensions.xs }]}>
            {comment.user.name}
          </Text>
          
          {comment.user.verified && (
            <Text style={{ color: colors.blue, fontSize: 12, marginRight: dimensions.xs }}>
              ✓
            </Text>
          )}
          
          <Text style={[commonStyles.bodySmall, { color: colors.mutedText }]}>
            {comment.timestamp}
          </Text>
        </View>

        <Text style={[commonStyles.bodyMedium, { marginBottom: dimensions.sm }]}>
          {comment.text}
        </Text>

        {/* Comment Actions */}
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <TouchableOpacity
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginRight: dimensions.lg,
            }}
            onPress={() => handleLikeComment(comment.id)}
          >
            <Text style={{
              color: comment.isLiked ? colors.red : colors.mutedText,
              fontSize: 14,
              marginRight: dimensions.xs,
            }}>
              {comment.isLiked ? '❤️' : '🤍'}
            </Text>
            <Text style={[commonStyles.bodySmall, { color: colors.mutedText }]}>
              {comment.likes}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity style={{ marginRight: dimensions.lg }}>
            <Text style={[commonStyles.bodySmall, { color: colors.mutedText }]}>
              Reply
            </Text>
          </TouchableOpacity>

          <TouchableOpacity>
            <Text style={[commonStyles.bodySmall, { color: colors.mutedText }]}>
              Share
            </Text>
          </TouchableOpacity>
        </View>

        {/* Replies */}
        {comment.replies && comment.replies.length > 0 && (
          <View style={{ marginTop: dimensions.sm }}>
            {comment.replies.map((reply) => (
              <CommentItem key={reply.id} comment={reply} isReply={true} />
            ))}
          </View>
        )}
      </View>
    </View>
  );

  if (!isVisible) return null;

  return (
    <View style={{
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: colors.black,
      zIndex: 1000,
    }}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Header */}
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          paddingHorizontal: dimensions.md,
          paddingVertical: dimensions.md,
          borderBottomWidth: 1,
          borderBottomColor: colors.border,
        }}>
          <Text style={[commonStyles.heading3]}>
            Comments ({comments.length})
          </Text>
          
          <TouchableOpacity onPress={onClose}>
            <Text style={{ color: colors.white, fontSize: 24 }}>✕</Text>
          </TouchableOpacity>
        </View>

        {/* Comments List */}
        <FlatList
          ref={flatListRef}
          data={comments}
          renderItem={({ item }) => <CommentItem comment={item} />}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
          style={{ flex: 1 }}
          contentContainerStyle={{ paddingBottom: dimensions.lg }}
        />

        {/* Comment Input */}
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          paddingHorizontal: dimensions.md,
          paddingVertical: dimensions.md,
          borderTopWidth: 1,
          borderTopColor: colors.border,
          backgroundColor: colors.darkGray,
        }}>
          <Image
            source={{ uri: user?.photoURL || 'https://via.placeholder.com/150' }}
            style={{
              width: 32,
              height: 32,
              borderRadius: 16,
              marginRight: dimensions.sm,
            }}
          />

          <TextInput
            style={[
              commonStyles.input,
              {
                flex: 1,
                marginRight: dimensions.sm,
                paddingVertical: dimensions.sm,
                minHeight: 40,
              }
            ]}
            placeholder="Add a comment..."
            placeholderTextColor={colors.mutedText}
            value={newComment}
            onChangeText={setNewComment}
            multiline
            maxLength={500}
          />

          <TouchableOpacity
            style={{
              backgroundColor: newComment.trim() ? colors.red : colors.lightGray,
              paddingHorizontal: dimensions.md,
              paddingVertical: dimensions.sm,
              borderRadius: dimensions.radiusMd,
              opacity: loading ? 0.7 : 1,
            }}
            onPress={handleSendComment}
            disabled={!newComment.trim() || loading}
          >
            <Text style={{
              color: colors.white,
              fontWeight: 'bold',
            }}>
              {loading ? '...' : 'Post'}
            </Text>
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </View>
  );
};

export default CommentSection;
