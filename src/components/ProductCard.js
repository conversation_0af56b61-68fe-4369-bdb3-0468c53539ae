import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  Dimensions,
  ScrollView,
  Animated,
} from 'react-native';
import { colors, dimensions, commonStyles } from '../styles/tiktokStyles';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
const cardHeight = screenHeight - 200; // Account for navigation bars

const ProductCard = ({
  product,
  isActive,
  onLike,
  onAddToCart,
  onShare,
  onComment,
  onTraderPress,
  onSwipeLeft,
  isInCart,
}) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isLiked, setIsLiked] = useState(product.isLiked || false);
  const [showHearts, setShowHearts] = useState([]);
  const scrollViewRef = useRef(null);
  const likeAnimation = useRef(new Animated.Value(1)).current;
  const heartAnimations = useRef([]).current;

  const handleLike = () => {
    setIsLiked(!isLiked);

    // Animate like button
    Animated.sequence([
      Animated.timing(likeAnimation, {
        toValue: 1.3,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(likeAnimation, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();

    onLike();
  };

  // Double tap to like (TikTok style)
  const handleDoubleTap = (event) => {
    const { locationX, locationY } = event.nativeEvent;

    if (!isLiked) {
      setIsLiked(true);
      onLike();

      // Create floating heart animation
      const heartId = Date.now();
      const heartAnim = new Animated.Value(0);
      heartAnimations.push({ id: heartId, animation: heartAnim });

      setShowHearts(prev => [...prev, {
        id: heartId,
        x: locationX - 25,
        y: locationY - 25,
        animation: heartAnim
      }]);

      // Animate floating heart
      Animated.sequence([
        Animated.timing(heartAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(heartAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start(() => {
        // Remove heart after animation
        setShowHearts(prev => prev.filter(heart => heart.id !== heartId));
      });
    }
  };

  // Simple swipe handler for trader page
  const handleSwipeLeft = () => {
    if (onSwipeLeft) {
      onSwipeLeft();
    }
  };

  const handleImageScroll = (event) => {
    const contentOffset = event.nativeEvent.contentOffset.x;
    const index = Math.round(contentOffset / screenWidth);
    setCurrentImageIndex(index);
  };

  const formatNumber = (num) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const ActionButton = ({ icon, count, onPress, isActive = false, color = colors.white }) => (
    <TouchableOpacity
      style={{
        alignItems: 'center',
        marginVertical: dimensions.md,
      }}
      onPress={onPress}
    >
      <View style={{
        width: 48,
        height: 48,
        borderRadius: 24,
        backgroundColor: isActive ? colors.red : 'rgba(255, 255, 255, 0.2)',
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: dimensions.xs,
      }}>
        <Text style={{
          fontSize: 20,
          color: isActive ? colors.white : color,
        }}>
          {icon}
        </Text>
      </View>
      {count !== undefined && (
        <Text style={{
          fontSize: 12,
          color: colors.white,
          fontWeight: '600',
        }}>
          {formatNumber(count)}
        </Text>
      )}
    </TouchableOpacity>
  );

  return (
    <View style={{
      width: screenWidth,
      height: cardHeight,
      backgroundColor: colors.black,
    }}>
        {/* Product Images/Videos */}
        <View style={{ flex: 1 }}>
          <TouchableOpacity
            activeOpacity={1}
            onPress={handleDoubleTap}
            style={{ flex: 1 }}
          >
            <ScrollView
              ref={scrollViewRef}
              horizontal
              pagingEnabled
              showsHorizontalScrollIndicator={false}
              onScroll={handleImageScroll}
              scrollEventThrottle={16}
            >
              {product.images?.map((image, index) => (
                <Image
                  key={index}
                  source={{ uri: image }}
                  style={{
                    width: screenWidth,
                    height: cardHeight,
                  }}
                  resizeMode="cover"
                />
              ))}
            </ScrollView>
          </TouchableOpacity>

          {/* Floating Hearts Animation */}
          {showHearts.map((heart) => (
            <Animated.View
              key={heart.id}
              style={{
                position: 'absolute',
                left: heart.x,
                top: heart.y,
                transform: [
                  {
                    scale: heart.animation.interpolate({
                      inputRange: [0, 0.5, 1],
                      outputRange: [0, 1.2, 0],
                    }),
                  },
                  {
                    translateY: heart.animation.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0, -100],
                    }),
                  },
                ],
                opacity: heart.animation.interpolate({
                  inputRange: [0, 0.5, 1],
                  outputRange: [0, 1, 0],
                }),
              }}
            >
              <Text style={{ fontSize: 50, color: colors.red }}>❤️</Text>
            </Animated.View>
          ))}

        {/* Image indicators */}
        {product.images?.length > 1 && (
          <View style={{
            position: 'absolute',
            bottom: dimensions.xl * 3,
            left: 0,
            right: 0,
            flexDirection: 'row',
            justifyContent: 'center',
          }}>
            {product.images.map((_, index) => (
              <View
                key={index}
                style={{
                  width: 8,
                  height: 8,
                  borderRadius: 4,
                  backgroundColor: index === currentImageIndex ? colors.white : 'rgba(255, 255, 255, 0.5)',
                  marginHorizontal: 4,
                }}
              />
            ))}
          </View>
        )}
      </View>

      {/* Right side actions */}
      <View style={{
        position: 'absolute',
        right: dimensions.md,
        bottom: dimensions.xl * 2,
        alignItems: 'center',
      }}>
        <Animated.View style={{ transform: [{ scale: likeAnimation }] }}>
          <ActionButton
            icon={isLiked ? "❤️" : "🤍"}
            count={product.likes + (isLiked ? 1 : 0)}
            onPress={handleLike}
            isActive={isLiked}
            color={isLiked ? colors.red : colors.white}
          />
        </Animated.View>

        <ActionButton
          icon="💬"
          count={product.comments}
          onPress={onComment}
        />

        <ActionButton
          icon="📤"
          count={product.shares}
          onPress={onShare}
        />

        <ActionButton
          icon={isInCart ? "✅" : "🛒"}
          onPress={onAddToCart}
          isActive={isInCart}
        />

        {/* Seller avatar */}
        <TouchableOpacity
          style={{
            marginTop: dimensions.lg,
            alignItems: 'center',
          }}
          onPress={onTraderPress}
        >
          <Image
            source={{ uri: 'https://via.placeholder.com/48' }}
            style={{
              width: 48,
              height: 48,
              borderRadius: 24,
              borderWidth: 2,
              borderColor: colors.white,
            }}
          />
          <View style={{
            position: 'absolute',
            bottom: -6,
            backgroundColor: colors.red,
            width: 20,
            height: 20,
            borderRadius: 10,
            alignItems: 'center',
            justifyContent: 'center',
          }}>
            <Text style={{
              color: colors.white,
              fontSize: 12,
              fontWeight: 'bold',
            }}>
              +
            </Text>
          </View>
        </TouchableOpacity>
      </View>

      {/* Bottom content */}
      <View style={{
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: dimensions.xl * 4,
        padding: dimensions.md,
        paddingBottom: dimensions.xl,
      }}>
        {/* Product info */}
        <View style={{ marginBottom: dimensions.md }}>
          <Text style={[commonStyles.heading3, { marginBottom: dimensions.xs }]}>
            {product.name}
          </Text>
          
          <Text style={[commonStyles.bodyMedium, { marginBottom: dimensions.sm }]}>
            {product.description}
          </Text>

          <View style={[commonStyles.row, { alignItems: 'center', marginBottom: dimensions.sm }]}>
            <Text style={{
              fontSize: 24,
              fontWeight: 'bold',
              color: colors.red,
              marginRight: dimensions.sm,
            }}>
              ${product.price}
            </Text>
            
            <View style={{
              backgroundColor: colors.red,
              paddingHorizontal: dimensions.sm,
              paddingVertical: dimensions.xs,
              borderRadius: dimensions.radiusSm,
            }}>
              <Text style={{
                color: colors.white,
                fontSize: 12,
                fontWeight: 'bold',
              }}>
                LIMITED
              </Text>
            </View>
          </View>

          {/* Hashtags */}
          <View style={[commonStyles.row, { flexWrap: 'wrap' }]}>
            <Text style={[commonStyles.bodySmall, { color: colors.blue }]}>
              #{product.category} #trending #musthave
            </Text>
          </View>
        </View>

        {/* Add to cart button */}
        <TouchableOpacity
          style={[
            commonStyles.primaryButton,
            {
              backgroundColor: isInCart ? colors.green : colors.red,
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
            }
          ]}
          onPress={onAddToCart}
        >
          <Text style={{
            color: colors.white,
            fontSize: 16,
            fontWeight: 'bold',
            marginRight: dimensions.xs,
          }}>
            {isInCart ? 'Added to Cart' : 'Add to Cart'}
          </Text>
          <Text style={{ fontSize: 16 }}>
            {isInCart ? '✅' : '🛒'}
          </Text>
        </TouchableOpacity>

        {/* Swipe Indicator */}
        <TouchableOpacity
          style={{
            position: 'absolute',
            right: 10,
            top: '50%',
            transform: [{ translateY: -25 }],
            opacity: 0.6,
          }}
          onPress={handleSwipeLeft}
        >
          <Text style={{
            color: colors.white,
            fontSize: 12,
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            paddingHorizontal: dimensions.xs,
            paddingVertical: 4,
            borderRadius: 4,
            textAlign: 'center',
          }}>
            ← Tap{'\n'}for seller
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default ProductCard;
