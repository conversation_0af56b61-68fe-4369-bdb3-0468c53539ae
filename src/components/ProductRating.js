import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  TextInput,
  ScrollView,
  Alert,
} from 'react-native';
import { colors, dimensions, commonStyles } from '../styles/tiktokStyles';

const ProductRating = ({ 
  productId, 
  currentRating = 0, 
  totalReviews = 0,
  onRatingSubmit,
  showModal = false,
  onClose 
}) => {
  const [rating, setRating] = useState(0);
  const [review, setReview] = useState('');
  const [submitting, setSubmitting] = useState(false);

  const StarRating = ({ rating, onRatingChange, interactive = false, size = 20 }) => {
    return (
      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
        {[1, 2, 3, 4, 5].map((star) => (
          <TouchableOpacity
            key={star}
            onPress={() => interactive && onRatingChange && onRatingChange(star)}
            disabled={!interactive}
            style={{ marginRight: 4 }}
          >
            <Text style={{
              fontSize: size,
              color: star <= rating ? colors.yellow : colors.mutedText,
            }}>
              ⭐
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const handleSubmitReview = async () => {
    if (rating === 0) {
      Alert.alert('Rating Required', 'Please select a rating before submitting.');
      return;
    }

    if (review.trim().length < 10) {
      Alert.alert('Review Too Short', 'Please write at least 10 characters for your review.');
      return;
    }

    try {
      setSubmitting(true);
      
      // Submit review (mock implementation)
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      if (onRatingSubmit) {
        onRatingSubmit({
          productId,
          rating,
          review: review.trim(),
          timestamp: new Date().toISOString(),
        });
      }

      Alert.alert('Success!', 'Thank you for your review!');
      setRating(0);
      setReview('');
      onClose && onClose();
      
    } catch (error) {
      Alert.alert('Error', 'Failed to submit review. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const RatingDisplay = () => (
    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
      <StarRating rating={currentRating} size={16} />
      <Text style={[commonStyles.bodySmall, { color: colors.secondaryText, marginLeft: dimensions.sm }]}>
        {currentRating.toFixed(1)} ({totalReviews} reviews)
      </Text>
    </View>
  );

  if (!showModal) {
    return <RatingDisplay />;
  }

  return (
    <Modal
      visible={showModal}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={{
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        justifyContent: 'flex-end',
      }}>
        <View style={{
          backgroundColor: colors.darkGray,
          borderTopLeftRadius: dimensions.radiusLg,
          borderTopRightRadius: dimensions.radiusLg,
          paddingHorizontal: dimensions.lg,
          paddingTop: dimensions.lg,
          paddingBottom: dimensions.xl,
          maxHeight: '80%',
        }}>
          {/* Header */}
          <View style={[commonStyles.row, commonStyles.spaceBetween, { marginBottom: dimensions.lg }]}>
            <Text style={[commonStyles.heading3]}>
              Rate this Product
            </Text>
            <TouchableOpacity onPress={onClose}>
              <Text style={{ color: colors.white, fontSize: 24 }}>✕</Text>
            </TouchableOpacity>
          </View>

          <ScrollView showsVerticalScrollIndicator={false}>
            {/* Current Rating Display */}
            <View style={{
              backgroundColor: colors.mediumGray,
              borderRadius: dimensions.radiusMd,
              padding: dimensions.md,
              marginBottom: dimensions.lg,
              alignItems: 'center',
            }}>
              <Text style={[commonStyles.bodyMedium, { marginBottom: dimensions.sm }]}>
                Current Rating
              </Text>
              <RatingDisplay />
            </View>

            {/* Rating Input */}
            <View style={{ marginBottom: dimensions.lg }}>
              <Text style={[commonStyles.bodyLarge, { fontWeight: 'bold', marginBottom: dimensions.md }]}>
                Your Rating
              </Text>
              <View style={{ alignItems: 'center', marginBottom: dimensions.md }}>
                <StarRating 
                  rating={rating} 
                  onRatingChange={setRating} 
                  interactive={true}
                  size={32}
                />
                <Text style={[commonStyles.bodyMedium, { color: colors.secondaryText, marginTop: dimensions.sm }]}>
                  {rating === 0 ? 'Tap to rate' : 
                   rating === 1 ? 'Poor' :
                   rating === 2 ? 'Fair' :
                   rating === 3 ? 'Good' :
                   rating === 4 ? 'Very Good' : 'Excellent'}
                </Text>
              </View>
            </View>

            {/* Review Input */}
            <View style={{ marginBottom: dimensions.lg }}>
              <Text style={[commonStyles.bodyLarge, { fontWeight: 'bold', marginBottom: dimensions.md }]}>
                Write a Review (Optional)
              </Text>
              <TextInput
                style={[
                  commonStyles.input,
                  {
                    height: 120,
                    textAlignVertical: 'top',
                    paddingTop: dimensions.md,
                  }
                ]}
                placeholder="Share your experience with this product..."
                placeholderTextColor={colors.mutedText}
                value={review}
                onChangeText={setReview}
                multiline
                maxLength={500}
              />
              <Text style={[commonStyles.bodySmall, { color: colors.mutedText, textAlign: 'right', marginTop: dimensions.xs }]}>
                {review.length}/500
              </Text>
            </View>

            {/* Quick Review Options */}
            <View style={{ marginBottom: dimensions.lg }}>
              <Text style={[commonStyles.bodyMedium, { fontWeight: 'bold', marginBottom: dimensions.md }]}>
                Quick Reviews
              </Text>
              <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
                {[
                  'Great quality!',
                  'Fast shipping',
                  'As described',
                  'Good value',
                  'Highly recommend',
                  'Perfect fit',
                ].map((quickReview) => (
                  <TouchableOpacity
                    key={quickReview}
                    style={{
                      backgroundColor: review === quickReview ? colors.red : colors.mediumGray,
                      borderRadius: dimensions.radiusMd,
                      paddingHorizontal: dimensions.md,
                      paddingVertical: dimensions.sm,
                      marginRight: dimensions.sm,
                      marginBottom: dimensions.sm,
                    }}
                    onPress={() => setReview(quickReview)}
                  >
                    <Text style={{
                      color: review === quickReview ? colors.white : colors.secondaryText,
                      fontSize: 12,
                    }}>
                      {quickReview}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Submit Button */}
            <TouchableOpacity
              style={[
                commonStyles.primaryButton,
                {
                  opacity: rating === 0 || submitting ? 0.5 : 1,
                  marginBottom: dimensions.md,
                }
              ]}
              onPress={handleSubmitReview}
              disabled={rating === 0 || submitting}
            >
              <Text style={[commonStyles.bodyLarge, { color: colors.white, fontWeight: 'bold' }]}>
                {submitting ? 'Submitting...' : 'Submit Review'}
              </Text>
            </TouchableOpacity>

            {/* Cancel Button */}
            <TouchableOpacity
              style={[commonStyles.secondaryButton]}
              onPress={onClose}
            >
              <Text style={[commonStyles.bodyMedium, { color: colors.white }]}>
                Cancel
              </Text>
            </TouchableOpacity>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

export default ProductRating;
