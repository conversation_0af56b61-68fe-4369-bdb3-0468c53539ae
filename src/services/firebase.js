// Firebase configuration for TikTok-style ecommerce app
// Note: Replace with your actual Firebase config

// For now, we'll create a mock Firebase service
// In production, you would install and configure Firebase SDK

const mockFirebaseConfig = {
  apiKey: "your-api-key",
  authDomain: "store-tok.firebaseapp.com",
  projectId: "store-tok",
  storageBucket: "store-tok.appspot.com",
  messagingSenderId: "123456789",
  appId: "your-app-id"
};

// Mock Firebase Auth
export const auth = {
  currentUser: null,
  
  signInWithEmailAndPassword: async (email, password) => {
    // Mock authentication
    return new Promise((resolve) => {
      setTimeout(() => {
        const user = {
          uid: 'mock-user-id',
          email: email,
          displayName: email.split('@')[0],
          photoURL: 'https://via.placeholder.com/150',
        };
        auth.currentUser = user;
        resolve({ user });
      }, 1000);
    });
  },
  
  createUserWithEmailAndPassword: async (email, password) => {
    // Mock user creation
    return new Promise((resolve) => {
      setTimeout(() => {
        const user = {
          uid: 'mock-user-id-' + Date.now(),
          email: email,
          displayName: email.split('@')[0],
          photoURL: 'https://via.placeholder.com/150',
        };
        auth.currentUser = user;
        resolve({ user });
      }, 1000);
    });
  },
  
  signOut: async () => {
    return new Promise((resolve) => {
      setTimeout(() => {
        auth.currentUser = null;
        resolve();
      }, 500);
    });
  },
  
  onAuthStateChanged: (callback) => {
    // Mock auth state listener
    callback(auth.currentUser);
    return () => {}; // unsubscribe function
  }
};

// Mock Firestore
export const firestore = {
  collection: (collectionName) => ({
    doc: (docId) => ({
      get: async () => ({
        exists: true,
        data: () => mockData[collectionName]?.[docId] || {},
        id: docId,
      }),
      
      set: async (data) => {
        if (!mockData[collectionName]) {
          mockData[collectionName] = {};
        }
        mockData[collectionName][docId] = data;
        return Promise.resolve();
      },
      
      update: async (data) => {
        if (!mockData[collectionName]) {
          mockData[collectionName] = {};
        }
        mockData[collectionName][docId] = {
          ...mockData[collectionName][docId],
          ...data
        };
        return Promise.resolve();
      },
      
      delete: async () => {
        if (mockData[collectionName]?.[docId]) {
          delete mockData[collectionName][docId];
        }
        return Promise.resolve();
      }
    }),
    
    add: async (data) => {
      const docId = 'mock-doc-' + Date.now();
      if (!mockData[collectionName]) {
        mockData[collectionName] = {};
      }
      mockData[collectionName][docId] = { ...data, id: docId };
      return Promise.resolve({ id: docId });
    },
    
    get: async () => {
      const docs = Object.entries(mockData[collectionName] || {}).map(([id, data]) => ({
        id,
        data: () => data,
        exists: true,
      }));
      return Promise.resolve({ docs });
    },
    
    where: (field, operator, value) => ({
      get: async () => {
        const docs = Object.entries(mockData[collectionName] || {})
          .filter(([id, data]) => {
            switch (operator) {
              case '==':
                return data[field] === value;
              case '!=':
                return data[field] !== value;
              case '>':
                return data[field] > value;
              case '<':
                return data[field] < value;
              case '>=':
                return data[field] >= value;
              case '<=':
                return data[field] <= value;
              case 'array-contains':
                return Array.isArray(data[field]) && data[field].includes(value);
              default:
                return true;
            }
          })
          .map(([id, data]) => ({
            id,
            data: () => data,
            exists: true,
          }));
        return Promise.resolve({ docs });
      }
    }),
    
    orderBy: (field, direction = 'asc') => ({
      get: async () => {
        const docs = Object.entries(mockData[collectionName] || {})
          .sort(([, a], [, b]) => {
            if (direction === 'desc') {
              return b[field] > a[field] ? 1 : -1;
            }
            return a[field] > b[field] ? 1 : -1;
          })
          .map(([id, data]) => ({
            id,
            data: () => data,
            exists: true,
          }));
        return Promise.resolve({ docs });
      }
    })
  })
};

// Mock Storage
export const storage = {
  ref: (path) => ({
    put: async (file) => {
      // Mock file upload
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            ref: {
              getDownloadURL: async () => `https://mock-storage.com/${path}/${Date.now()}`
            }
          });
        }, 2000);
      });
    },
    
    getDownloadURL: async () => {
      return `https://mock-storage.com/${path}`;
    }
  })
};

// Mock data for development
const mockData = {
  products: {
    'product1': {
      id: 'product1',
      name: 'Trendy Sneakers',
      price: 129.99,
      description: 'Super comfortable and stylish sneakers perfect for any occasion.',
      images: [
        'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400',
        'https://images.unsplash.com/photo-1560769629-975ec94e6a86?w=400',
      ],
      videos: [
        'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4'
      ],
      category: 'footwear',
      likes: 1250,
      comments: 89,
      shares: 45,
      sellerId: 'seller1',
      createdAt: new Date().toISOString(),
    },
    'product2': {
      id: 'product2',
      name: 'Wireless Earbuds',
      price: 79.99,
      description: 'High-quality wireless earbuds with noise cancellation.',
      images: [
        'https://images.unsplash.com/photo-1572569511254-d8f925fe2cbb?w=400',
        'https://images.unsplash.com/photo-1590658268037-6bf12165a8df?w=400',
      ],
      videos: [],
      category: 'electronics',
      likes: 890,
      comments: 67,
      shares: 23,
      sellerId: 'seller2',
      createdAt: new Date().toISOString(),
    }
  },
  
  users: {
    'seller1': {
      id: 'seller1',
      name: 'Fashion Store',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
      followers: 15600,
      following: 234,
      verified: true,
      bio: 'Your favorite fashion destination 👟✨',
    },
    'seller2': {
      id: 'seller2',
      name: 'Tech Hub',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
      followers: 8900,
      following: 156,
      verified: true,
      bio: 'Latest tech gadgets and accessories 🎧📱',
    }
  },
  
  comments: {},
  orders: {},
  cart: {}
};

export default {
  auth,
  firestore,
  storage,
  mockData
};
