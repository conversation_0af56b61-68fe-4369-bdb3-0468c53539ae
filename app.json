{"expo": {"name": "StoreTok", "slug": "store-tok", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "dark", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#000000"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "statusBarStyle": "light"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#000000"}, "edgeToEdgeEnabled": true, "statusBarStyle": "light", "navigationBarStyle": "dark"}, "web": {"favicon": "./assets/favicon.png"}, "plugins": [["expo-image-picker", {"photosPermission": "The app accesses your photos to let you share them with your products.", "cameraPermission": "The app accesses your camera to let you take photos for your products."}], ["expo-media-library", {"photosPermission": "Allow StoreTok to access your photo library to save and share product images.", "savePhotosPermission": "Allow StoreTok to save photos to your photo library.", "isAccessMediaLocationEnabled": true}]]}}