# StoreTok - TikTok-Style Ecommerce App

A TikTok-style ecommerce mobile application built with React Native and Expo, featuring vertical scrolling product feeds, social interactions, and seamless shopping experience.

## Features

### 🎥 TikTok-Style Interface
- **Vertical Scrolling Feed**: Full-screen product videos and images with smooth scrolling
- **Auto-play Videos**: Intersection observer for automatic video playback
- **Snap-to-Video**: Perfect TikTok-like scrolling experience
- **Immersive UI**: Full-screen content with overlay controls

### 🛒 Ecommerce Features
- **Product Catalog**: Multiple images/videos per product
- **Shopping Cart**: TikTok-style cart with quantity controls
- **Real-time Pricing**: Dynamic pricing with special offers
- **Secure Checkout**: Streamlined checkout process

### 👥 Social Features
- **Like System**: Heart animations and like counts
- **Comments**: Full comment system with replies
- **Share Products**: Social sharing functionality
- **User Reviews**: Product reviews and ratings

### 🏪 Trader/Seller Features
- **Seller Profiles**: Swipe left to view trader pages
- **Follower System**: Follow/unfollow sellers
- **Seller Stats**: Followers, products, ratings
- **Verified Badges**: Verified seller indicators

### 🔐 User Management
- **Firebase Authentication**: Email/password and social login
- **User Profiles**: Complete profile management
- **Order History**: Track purchases and orders
- **Wishlist**: Save favorite products

## Technology Stack

- **Frontend**: React Native with Expo
- **Backend**: Firebase (Firestore, Auth, Storage)
- **Navigation**: Custom TikTok-style navigation
- **State Management**: Context API
- **Styling**: Custom TikTok-inspired design system
- **Media**: Expo AV for video playback
- **Storage**: AsyncStorage for cart persistence

## Project Structure

```
store-tok/
├── App.js                          # Main app component
├── src/
│   ├── components/                 # Reusable components
│   │   ├── ProductCard.js         # TikTok-style product card
│   │   ├── VideoPlayer.js         # Video player component
│   │   └── CommentSection.js      # Comments overlay
│   ├── screens/                   # App screens
│   │   ├── HomeScreen.js          # Main TikTok-style feed
│   │   ├── CartScreen.js          # Shopping cart
│   │   ├── ProfileScreen.js       # User profile
│   │   ├── AuthScreen.js          # Authentication
│   │   └── TraderScreen.js        # Seller profiles
│   ├── navigation/                # Navigation setup
│   │   └── MainNavigator.js       # Tab navigation
│   ├── context/                   # State management
│   │   ├── AuthContext.js         # Authentication state
│   │   └── CartContext.js         # Shopping cart state
│   ├── services/                  # External services
│   │   └── firebase.js            # Firebase configuration
│   └── styles/                    # Styling
│       └── tiktokStyles.js        # TikTok-inspired design system
└── assets/                        # Static assets
```

## Installation & Setup

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn
- Expo CLI
- iOS Simulator or Android Emulator (optional)

### Installation Steps

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd store-tok
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm start
   ```

4. **Run on device/simulator**
   - Scan QR code with Expo Go app (iOS/Android)
   - Press 'i' for iOS simulator
   - Press 'a' for Android emulator

## Firebase Setup (Optional)

To use real Firebase instead of mock data:

1. Create a Firebase project at [Firebase Console](https://console.firebase.google.com)
2. Enable Authentication, Firestore, and Storage
3. Replace the mock configuration in `src/services/firebase.js`
4. Add your Firebase config object

## Key Features Implementation

### TikTok-Style Scrolling
- Uses `ScrollView` with `pagingEnabled` and `snapToInterval`
- Intersection Observer API for auto-play functionality
- Full-screen product cards with overlay controls

### Social Interactions
- Like animations with heart effects
- Comment system with real-time updates
- Share functionality for products

### Shopping Experience
- Add to cart with visual feedback
- Persistent cart using AsyncStorage
- Smooth checkout process

### Seller Integration
- Swipe left gesture to view seller profiles
- Follow/unfollow functionality
- Seller verification system

## Design System

The app uses a custom TikTok-inspired design system with:

- **Colors**: Black background, red accents, white text
- **Typography**: System fonts with TikTok-style sizing
- **Components**: Reusable UI components
- **Animations**: Smooth transitions and micro-interactions

## Demo Credentials

For testing the app:
- **Email**: <EMAIL>
- **Password**: demo123

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Acknowledgments

- Inspired by TikTok's innovative UI/UX design
- Built with React Native and Expo
- Firebase for backend services

---

**StoreTok** - Where shopping meets social media! 🛍️📱
