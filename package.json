{"name": "store-tok", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.24.0", "@react-navigation/bottom-tabs": "^7.4.4", "@react-navigation/native": "^7.1.16", "@react-navigation/stack": "^7.4.4", "expo": "~53.0.20", "expo-av": "^15.1.7", "expo-image-picker": "^16.1.4", "expo-media-library": "^17.1.7", "expo-status-bar": "~2.2.3", "firebase": "^12.0.0", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "^2.27.2", "react-native-reanimated": "^4.0.1", "react-native-safe-area-context": "^5.5.2", "react-native-screens": "^4.13.1", "react-native-snap-carousel": "^3.9.1", "react-native-super-grid": "^6.0.1", "react-native-vector-icons": "^10.3.0"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}